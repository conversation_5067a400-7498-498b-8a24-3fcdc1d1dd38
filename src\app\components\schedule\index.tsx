"use client";

import { useState, useEffect } from "react";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faXmark } from "@fortawesome/free-solid-svg-icons";

import { Button } from "@/app/components";
import { Select } from "@/app/components";
import { ScheduleModalProps, ScheduleState } from "../index.types";
import { DropDownList, GetPickupSlotsResponse, ScheduleTypes } from "@/types";

import { callPickupSlotsAbstractedAPI } from "@/lib/apiConfigs";
import { initialSchedule } from "@/lib/utils/constants";
import { generateScheduleDates } from "@/lib/utils/helperFunctions";
import { dispatch, useSelector } from "@/lib/store";
import { updateOrderStore } from "@/lib/store/slices/order";

const Schedule = ({ scheduleModalCallback }: ScheduleModalProps) => {
  const [schedule, setSchedule] = useState<ScheduleState>(initialSchedule);

  const [datesList, setDatesList] = useState<DropDownList[]>([]);
  const [timeSlots, setTimeSlots] = useState<string[]>([]);

  const { location, scheduleType } = useSelector((state) => state.order);

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function call to generate dates for order schedule
    getScheduleDates();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    // function to call API to get time slots for selected date
    if (schedule?.date?.value) {
      callGetPickupSlotsAPI();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [schedule?.date]);

  // ==============================|| handler functions ||============================== //

  // function to call get pickup slots API
  const callGetPickupSlotsAPI = async (): Promise<void> => {
    // get pickup slots abstracted API call
    const { data } = await callPickupSlotsAbstractedAPI({
      location: location?.id || "",
      date: schedule?.date?.value,
    });

    // verify if data exists and has slots
    if (Array.isArray((data as GetPickupSlotsResponse)?.slots)) {
      // update time slots state
      setTimeSlots((data as GetPickupSlotsResponse)?.slots);

      // update schedule state with the first time slot
      setSchedule((prevState) => ({
        ...prevState,
        time: (data as GetPickupSlotsResponse)?.slots?.[0] || "",
      }));
    }
  };

  // function to generate dates for schedule
  const getScheduleDates = (): void => {
    // get 7 days/dates list with respect to provided timeZone and schedule type
    const datesList: DropDownList[] = generateScheduleDates(
      location?.timeZone || "",
      scheduleType
    );

    // update state
    setDatesList(datesList);

    // update schedule state with the first date
    setSchedule((prevState) => ({
      ...prevState,
      date: datesList?.[0] || { label: "", value: "" },
    }));
  };

  // function to handle schedule change
  const handleScheduleChange = (
    event: React.ChangeEvent<HTMLSelectElement>
  ): void => {
    /**
     * if => title is "Time", update time
     * else => update date
     */
    if (event?.target?.title === "Time") {
      // update time
      setSchedule((prevState) => ({
        ...prevState,
        time: event?.target?.value || "",
      }));
    } else {
      // find the selected date
      const selectedDate: DropDownList = datesList?.find(
        (date) => date?.value === event?.target?.value
      ) || { label: "", value: "" };

      // update date
      setSchedule((prevState) => ({ ...prevState, date: selectedDate }));
    }
  };

  // function to handle save schedule
  const handleSaveSchedule = (): void => {
    // validate date selection
    if (!schedule?.date?.value) {
      return;
    }

    // validate time selection
    if (!schedule?.time) {
      return;
    }

    // save schedule to persisted store
    dispatch(updateOrderStore({ type: "schedule", value: schedule }));

    // call a callback function to close modal with navigation
    scheduleModalCallback(true);
  };

  // ==============================|| UI ||============================== //

  return (
    <div className="fixed inset-0 bg-black/50 z-10 flex items-start justify-center pt-11 px-8">
      <div className="bg-white rounded-lg w-full max-w-md schedule_modal_parent">
        {/* header with close button */}
        <div className="flex justify-between items-center schedule_modal_data">
          <h2 className="text-2xl font-black">{`PLEASE PICK A ${
            scheduleType === ScheduleTypes.WEEKLY ||
            scheduleType === ScheduleTypes.BIWEEKLY
              ? "DAY"
              : "DATE"
          } & TIME`}</h2>

          <Button
            variant="ghost"
            aria-label="Close schedule"
            className="!p-0"
            onClick={() => scheduleModalCallback()}
          >
            <FontAwesomeIcon icon={faXmark} />
          </Button>
        </div>

        <div className="schedule_modal_field">
          <Select
            title={scheduleType === ScheduleTypes.LATER ? "Date" : "Day"}
            placeholder={`Select a ${
              scheduleType === ScheduleTypes.LATER ? "date" : "day"
            }`}
            value={schedule?.date?.value || ""}
            options={datesList}
            className="h-11 mb-3"
            onChange={handleScheduleChange}
          />

          <Select
            title="Time"
            placeholder={
              timeSlots?.length
                ? "Select a time slot"
                : "Pickup slots not available"
            }
            value={schedule?.time || ""}
            options={timeSlots?.map((time) => ({ label: time, value: time }))}
            className="h-11 mb-3"
            disabled={!timeSlots?.length}
            onChange={handleScheduleChange}
          />
        </div>

        <Button
          variant="primary"
          onClick={handleSaveSchedule}
          className="schedule_modal_button"
        >
          SCHEDULE
        </Button>
      </div>
    </div>
  );
};

export default Schedule;
