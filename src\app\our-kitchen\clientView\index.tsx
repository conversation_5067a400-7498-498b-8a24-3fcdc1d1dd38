"use client";

import { useState } from "react";

const ClientView = () => {
  const [toggleDescription, setToggleDescription] = useState<boolean>(false);

  // ==============================|| UI ||============================== //

  return (
    <div className="custom_11">
      <figure className="custom_11_fig">
        <img
          className="custom_11_ab_1"
          src="https://static.tossdown.com/images/03dd9adf-abe7-4b33-93ab-000fca7b0eaf.webp"
          alt="About us"
        />
      </figure>

      <div className="custom_11_body_main">
        <div className="custom_11_body">
          <div className="custom_11_heading first_sec_heading">
            <figure className="ab_one">
              <img
                src="https://static.tossdown.com/site/f385ea8a-99ac-46bb-9ed9-38e7c4152c39.webp"
                alt="About icon"
              />
            </figure>

            <div>
              <div className="heading_box">
                <h3>ABOUT US</h3>
              </div>

              {(() => {
                const firstParagraph =
                  "EZeats is a convenient, delicious and affordable online meal solution for busy lives! Our platform offers a wide variety of delicious and flavorful meal options, carefully crafted by our team of experienced chefs and professionals. Our meals are made with fresh, locally-sourced ingredients and are designed to be quick and easy to prepare.";

                const secondParagraph =
                  "At EZeats, we are committed to delivering the highest quality food, prepared with the utmost care and attention to detail. Our meals are crafted in a state-of-the-art facility that meets the stringent standards of food safety, SQF certified, and globally recognized by GFSI. This ensures that every dish we serve is not only of the highest quality but also of the highest food safety. We take pride in using only Halal-certified meat & ingredients, ensuring that our dishes adhere to strictest halal guidelines and HMA certification. ";

                const truncatedSecondParagraph =
                  secondParagraph.substring(0, 100) + "... ";

                return (
                  <p>
                    {firstParagraph}
                    <br />
                    <br />

                    {toggleDescription
                      ? secondParagraph
                      : truncatedSecondParagraph}

                    <button
                      onClick={() => setToggleDescription(!toggleDescription)}
                      className="text-[#FFCC00] font-medium hover:underline focus:outline-none text-sm cursor-pointer"
                    >
                      {toggleDescription ? "Read less" : "Read more"}
                    </button>
                  </p>
                );
              })()}
            </div>

            <figure className="responsive_one">
              <img
                src="https://static.tossdown.com/site/c8c38c9b-4193-47f4-ae88-4615fb5f01a9.webp"
                alt="About icon responsive"
              />
            </figure>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientView;
