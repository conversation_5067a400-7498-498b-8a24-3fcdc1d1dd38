import React from "react";

import { SkeletonProps } from "../index.types";

const Skeleton: React.FC<SkeletonProps> = ({ rows = 1 }) => {
  return (
    <section className="py-8">
      <div className="static_container mx-auto px-4 lg:px-0">
        {/* container for all rows with vertical spacing */}
        <div className="space-y-6">
          {/* create the exact number of rows specified by the 'rows' prop */}
          {Array.from({ length: rows })?.map((_, rowIndex) => (
            <div
              key={rowIndex}
              className="grid gap-6 grid-cols-2 lg:grid-cols-4"
            >
              {/**
               * this approach ensures:
               * => small screens: Show 2 cards per row (grid-cols-2)
               * => large screens: Show 4 cards per row (lg:grid-cols-4)
               */}
              {Array.from({ length: 4 })?.map((_, itemIndex) => (
                <div
                  key={`${rowIndex}-${itemIndex}`}
                  className={`bg-white rounded-lg shadow-md overflow-hidden animate-pulse ${
                    itemIndex >= 2 ? "hidden lg:block" : ""
                  }`}
                >
                  {/* image skeleton */}
                  <div className="w-full h-48 bg-gray-200"></div>

                  {/* content skeleton */}
                  <div className="p-4">
                    {/* title skeleton */}
                    <div className="h-6 bg-gray-200 rounded-md mb-2"></div>

                    {/* description skeleton */}
                    <div className="space-y-2 mb-4">
                      <div className="h-4 bg-gray-200 rounded-md"></div>
                      <div className="h-4 bg-gray-200 rounded-md w-3/4"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Skeleton;
