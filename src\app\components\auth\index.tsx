"use client";

import { useState, useRef, useEffect } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faChevronCircleDown, faUser } from "@fortawesome/free-solid-svg-icons";

import { Button, ManualAuth } from "@/app/components";
import {
  cookiesKey,
  initialUserDetails,
  userAuthTypes,
} from "@/lib/utils/constants";
import { removeCookie } from "@/lib/utils/helperFunctions";
import { dispatch, useSelector } from "@/lib/store";
import { updateOrderStore } from "@/lib/store/slices/order";

const Auth: React.FC = () => {
  const [profileMenu, setProfileMenu] = useState<boolean>(false);
  const [toggleUserManualAuth, setToggleUserManualAuth] =
    useState<boolean>(false);

  const selectedAuthTab = useRef<string>(userAuthTypes?.LOGIN);
  const profileMenuRef = useRef<HTMLDivElement>(null);

  const { userDetails } = useSelector((state) => state?.order);

  const pathname = usePathname();

  // ==============================|| useEffect hooks ||============================== //

  useEffect(() => {
    if (profileMenu) {
      document.addEventListener("click", handleProfileMenuOutside);
    }

    return () => {
      document.removeEventListener("click", handleProfileMenuOutside);
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [profileMenu]);

  // ==============================|| handler functions ||============================== //

  // manual auth process handler
  const handleManualAuthProcess = (authTab: string): void => {
    // set the selected tab on modal
    selectedAuthTab.current = authTab;

    // open the modal
    handleToggleUserManualAuth();
  };

  // function to handle logout process
  const handleLogout = (): void => {
    // remove user details from cookie
    removeCookie(cookiesKey?.userDetails);

    // update store
    dispatch(
      updateOrderStore({ type: "userDetails", value: initialUserDetails })
    );

    // close profile menu
    toggleProfileMenu();
  };

  // function to toggle the modal state
  const handleToggleUserManualAuth = (): void => {
    setToggleUserManualAuth((prevState) => !prevState);
  };

  // function to handle profile click
  const handleProfileClick = (): void => {
    /**
     * if => user is logged in, open profile menu
     * else => open the auth modal
     */
    if (userDetails?.userId) {
      toggleProfileMenu();
    } else {
      handleManualAuthProcess(userAuthTypes?.LOGIN);
    }
  };

  // function to handle profile menu
  const toggleProfileMenu = (): void => {
    setProfileMenu((prevState) => !prevState);
  };

  // function to handle close profile menu on outside click
  const handleProfileMenuOutside = (event: MouseEvent) => {
    if (
      profileMenuRef.current &&
      !profileMenuRef.current?.contains(event.target as Node)
    ) {
      toggleProfileMenu();
    }
  };

  // ==============================|| UI ||============================== //

  // function to render auth icons
  const renderAuthIcons = (): React.ReactNode => {
    if (pathname?.startsWith("/checkout") || pathname?.startsWith("/profile")) {
      return null;
    }

    return (
      <div ref={profileMenuRef} className="relative profile-menu-container">
        <Button
          variant="ghost"
          className="flex items-center justify-center gap-1 !p-0"
          onClick={handleProfileClick}
        >
          <FontAwesomeIcon icon={faUser} size="lg" />

          {userDetails?.userId && (
            <FontAwesomeIcon icon={faChevronCircleDown} size="sm" />
          )}
        </Button>

        {profileMenu && (
          <div className="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg z-10">
            <div className="py-1">
              <Link
                href="/profile"
                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                Personal Information
              </Link>
              <Link
                href="/profile/subscriptions"
                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                Subscriptions
              </Link>
              <Link
                href="/profile/orders"
                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                View Orders
              </Link>
              <Link
                href="/profile/activity-log"
                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                Activity Log
              </Link>
              <button
                className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                onClick={handleLogout}
              >
                Logout
              </button>
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderAuthUI = () => {
    if (pathname?.startsWith("/checkout") || pathname?.startsWith("/profile")) {
      return null;
    }

    if (!userDetails?.userId) {
      return (
        <>
          <Button
            variant="outline"
            size="small"
            className="text-sm border border-black rounded-full !px-2 !py-0"
            onClick={() => handleManualAuthProcess(userAuthTypes?.LOGIN)}
          >
            LOGIN
          </Button>

          <Button
            variant="outline"
            size="small"
            className="text-sm border border-black rounded-full !px-1 !py-0"
            onClick={() => handleManualAuthProcess(userAuthTypes?.SIGN_UP)}
          >
            SIGN UP
          </Button>
        </>
      );
    } else {
      return renderAuthIcons();
    }
  };

  return (
    <>
      <div className="hidden md:flex space-x-3">{renderAuthUI()}</div>

      <div className="md:hidden">{renderAuthIcons()}</div>

      {/* login & signUp modal */}
      {toggleUserManualAuth && (
        <ManualAuth
          toggleUserManualAuth={toggleUserManualAuth}
          handleToggleUserManualAuth={handleToggleUserManualAuth}
          authTab={selectedAuthTab.current}
        />
      )}
    </>
  );
};

export default Auth;
