import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>_<PERSON> } from "next/font/google";
import type { Viewport } from "next";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "./components";
import "./globals.css";
import "../styles/index.styles.css";
import Provider from "@/lib/store/provider";

// Load Anton font for headings
const anton = Anton({
  weight: ["400"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-anton",
});

// Load Poppins font for body text
const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-poppins",
});

// Load Bebas Neue
const bebasNeue = Bebas_Neue({
  weight: ["400"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-bebas-neue",
});

// Viewport configuration
export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${anton.variable} ${poppins.variable} ${bebasNeue.variable} antialiased`}
      >
        <Provider>
          {/* header component */}
          <Header />

          {/* page content */}
          <main className="min-h-screen">{children}</main>

          {/* footer component */}
          <Footer />

          {/* toast component */}
          <Toast />

          {/* cart drawer component */}
          <Cart />
        </Provider>
      </body>
    </html>
  );
}
