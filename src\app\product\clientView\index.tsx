"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import ProductDetailContent from "../clientView/detailContent";
import Breadcrumbs, { useBreadcrumbs } from "@/app/components/breadCrumb";
// import { trackViewItem } from "@/utils/analytics";
import { safeRemoveLink } from "@/lib/utils/dom-utils";

export default function ProductDetailClient({
  product: initialProduct,
  error: initialError,
  productId,
}: {
  product: any;
  error: string | null;
  productId: string;
}) {
  const [product, setProduct] = useState(initialProduct);
  const [isLoading, setIsLoading] = useState(!initialProduct && !initialError);
  const [error, setError] = useState(initialError);
  const contentRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  // Inside the component, add this after the useState declarations
  const breadcrumbs = useBreadcrumbs(product?.name);

  // <PERSON>le going back
  const handleBack = () => {
    router.back();
  };

  // Update the handleSubmitReview function
  const handleSubmitReview = async (reviewData: {
    rating: number;
    name: string;
    comment: string;
    productId: string;
  }) => {
    try {
      // For now, we'll just add it to the product's reviews in state
      const newReview = {
        id: `review-${Date.now()}`,
        user_name: reviewData.name,
        date: new Date().toISOString().split("T")[0],
        rating: reviewData.rating,
        comment: reviewData.comment,
      };

      setProduct((prev) => ({
        ...prev,
        reviews: [...(prev.reviews || []), newReview],
      }));

      // Navigate back after submitting review
      router.back();
    } catch (error) {
      console.error("Error handling review submission:", error);
    }
  };

  // Load necessary resources when component mounts
  useEffect(() => {
    // Create a link element for custom product description CSS only
    const customCssLink = document.createElement("link");
    customCssLink.rel = "stylesheet";
    customCssLink.href = "/styles/product-description.css";
    customCssLink.id = "product-description-css-page";

    // Append the CSS link to the head
    document.head.appendChild(customCssLink);

    // Return a cleanup function to remove the element when the component unmounts
    return () => {
      safeRemoveLink(
        "product-description-css-page",
        "Failed to remove product description CSS"
      );
    };
  }, []);

  return (
    <>
      {/* Anti-Tailwind CSS Reset */}
      <style jsx global>
        {`
          /* Header link styles */
          header a {
            color: black !important;
            transition: color 0.2s ease;
          }

          header a:hover {
            color: #ffcc00 !important;
          }
        `}
      </style>

      {/* Added top spacing */}
      <div className="pt-8 md:pt-12">
        {/* Breadcrumbs */}
        <div className="static_container mx-auto px-2.5 lg:px-0 !mb-6">
          <Breadcrumbs items={breadcrumbs} />
        </div>

        <div className="static_container mx-auto px-2.5 lg:px-0">
          {isLoading ? (
            <div className="flex justify-center items-center p-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black"></div>
            </div>
          ) : error ? (
            <div className="p-8 text-center text-red-500">{error}</div>
          ) : product ? (
            <div className="">
              <ProductDetailContent
                product={product}
                onReviewSubmit={handleSubmitReview}
              />
            </div>
          ) : null}
        </div>
      </div>
    </>
  );
}
