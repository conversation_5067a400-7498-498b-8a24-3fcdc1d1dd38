"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";

type FormData = {
  name: string;
  email: string;
  phone: string;
  message: string;
  contact_us_id: number;
  fullname: string;
  mobile: string;
};

type FormErrors = {
  name?: string;
  email?: string;
  phone?: string;
  message?: string;
};

const ClientView = () => {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    phone: "",
    message: "",
    contact_us_id: 1,
    fullname: "",
    mobile: "",
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const router = useRouter();

  // ==============================|| handler functions ||============================== //

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    // Phone validation - optional but validate format if provided
    if (formData.phone && !/^[0-9\-+\s]+$/.test(formData.phone)) {
      newErrors.phone = "Please enter a valid phone number";
    }

    // Message validation
    if (!formData.message.trim()) {
      newErrors.message = "Message is required";
    }

    setErrors(newErrors);

    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Set fullname same as name for compatibility
    if (name === "name") {
      setFormData((prev) => ({
        ...prev,
        fullname: value,
      }));
    }

    // Set mobile same as phone for compatibility
    if (name === "phone") {
      setFormData((prev) => ({
        ...prev,
        mobile: value,
      }));
    }

    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Convert form data to FormData object to match PHP $_POST format
      const formDataToSend = new FormData();

      // Add all form fields to FormData
      Object.entries(formData).forEach(([key, value]) => {
        formDataToSend.append(key, value.toString());
      });

      // Submit the form data to the specified URL
      const response = await fetch(
        "https://ezeats.ordrz.com/website/new_contact_us",
        {
          method: "POST",
          body: formDataToSend,
        }
      );

      if (!response.ok) {
        throw new Error("Failed to submit form");
      }

      // Reset form
      setFormData({
        name: "",
        email: "",
        phone: "",
        message: "",
        contact_us_id: 1,
        fullname: "",
        mobile: "",
        // Removed country, city, and subject fields
      });

      // Redirect to home page
      router.push("/");
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // ==============================|| UI ||============================== //

  return (
    <div className="contact_container">
      <div className="contact_section_one_parent">
        <div className="contact_section_one_box">
          <form onSubmit={handleSubmit} noValidate>
            <div className="contact_page_form_content">
              <div className="contact_us_form_fields">
                <span>Name</span>
                <input
                  type="text"
                  placeholder="Enter your full name"
                  required
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                />
                {errors.name && <label className="error">{errors.name}</label>}
              </div>
              <div className="contact_us_form_fields">
                <span>Email</span>
                <input
                  type="email"
                  placeholder="Enter your email"
                  required
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                />
                {errors.email && (
                  <label className="error">{errors.email}</label>
                )}
              </div>
              <div className="contact_us_form_fields">
                <span>Phone No</span>
                <input
                  type="text"
                  placeholder="Enter your contact number"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                />
                {errors.phone && (
                  <label className="error">{errors.phone}</label>
                )}
              </div>
              <div className="contact_us_form_fields">
                <span>Message</span>
                <textarea
                  placeholder="Write your message here...."
                  required
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  rows={4}
                />
                {errors.message && (
                  <label className="error">{errors.message}</label>
                )}
              </div>
              <div className="contact_us_form_submit_btn">
                <button type="submit" disabled={isSubmitting}>
                  <i className="far fa-paper-plane"></i>
                  {isSubmitting ? "SENDING..." : "SEND MESSAGE"}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ClientView;
