"use client";

import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faXmark, faEye, faEyeSlash } from "@fortawesome/free-solid-svg-icons";

import { Button, Tabs } from "@/app/components";

import { PaymentDetailsType, UserAuthResponse, UserDetails } from "@/types";
import { cookiesKey, userAuthTypes } from "@/lib/utils/constants";
import { updateCookies } from "@/lib/utils/helperFunctions";
import { updateOrderStore } from "@/lib/store/slices/order";
import { useSelector, dispatch } from "@/lib/store";
import { displayToast } from "@/lib/store/slices/toast";

// Define the props interface
interface ManualAuthProps {
  toggleUserManualAuth: boolean;
  handleToggleUserManualAuth: () => void;
  authTab: string;
}

const ManualAuth: React.FC<ManualAuthProps> = ({
  toggleUserManualAuth,
  handleToggleUserManualAuth,
  authTab,
}) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [phone, setPhone] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<string>(authTab);
  const modalRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  const { userDetails } = useSelector((state) => state?.order);

  // Get business ID from environment
  const BUSINESS_ID = process.env.NEXT_PUBLIC_BUSINESS_ID;

  // Helper function to show toast
  const showToast = (message: string, type: string = "error") => {
    dispatch(displayToast({ display: true, type, message }));
  };

  // Handle click outside to close modal
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        handleToggleUserManualAuth();
      }
    };

    if (toggleUserManualAuth) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [toggleUserManualAuth, handleToggleUserManualAuth]);

  // Handle ESC key to close modal
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        handleToggleUserManualAuth();
      }
    };

    if (toggleUserManualAuth) {
      document.addEventListener("keydown", handleEscKey);
    }

    return () => {
      document.removeEventListener("keydown", handleEscKey);
    };
  }, [toggleUserManualAuth, handleToggleUserManualAuth]);

  const setUserAuthResponse = (
    paymentDetails: PaymentDetailsType,
    userDetails: UserAuthResponse
  ): void => {
    const userAuthResponse: UserDetails = {
      name: userDetails?.user_fullname,
      email: userDetails?.user_email,
      phone: userDetails?.user_cphone,

      userId: userDetails?.user_id,
      tdUserId: userDetails?.td_user_id,
      authToken: userDetails?.token,
      renewAuthToken: userDetails?.refresh_token,

      // stripeCustomerId => if customer is registered on stripe (card saved on stripe)
      paymentDetails: {
        ...paymentDetails,
        stripe: {
          ...paymentDetails?.stripe,
          stripeCustomerId: userDetails?.payment_settings?.stripe || "",
        },
      },
    };

    // set user details in store
    dispatch(
      updateOrderStore({ type: "userDetails", value: userAuthResponse })
    );

    // set data in cookies
    updateCookies(cookiesKey.userDetails, userAuthResponse);
  };

  // Handle form submission for both login and signup
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      if (activeTab === userAuthTypes.RESET_PASSWORD) {
        // Handle forgot password
        const response = await fetch(
          `https://td0c8x9qb3.execute-api.us-east-1.amazonaws.com/prod/v1/business/${BUSINESS_ID}/user/forgot-password`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email,
              domain: "ezeats.ca",
            }),
          }
        );

        const data = await response.json();

        if (response.ok && data.status === 200) {
          // Show success message
          showToast(
            "Password reset link sent successfully to your email",
            "success"
          );

          // Close the modal
          handleToggleUserManualAuth();
        } else {
          // Show error message
          showToast(
            data.message ||
              "Failed to send password reset link. Please try again.",
            "error"
          );
        }
      } else if (activeTab === userAuthTypes.SIGN_UP) {
        // Handle signup
        // Format phone number if needed (ensure it has country code)
        const formattedPhone = phone.startsWith("+") ? phone : `+${phone}`;

        const response = await fetch(
          `https://td0c8x9qb3.execute-api.us-east-1.amazonaws.com/prod/v1/business/${BUSINESS_ID}/user/signup`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              name,
              email,
              phone: formattedPhone,
              password,
              address: "",
              city: "",
              gender: "",
              dob: "",
              token: "",
              source: "web",
              device: "web",
              facebook_id: "",
              google_id: "",
              apple_id: "",
            }),
          }
        );

        const data = await response.json();

        if (response.ok && data.status === 200) {
          // Store user data in cookies and update store
          setUserAuthResponse(userDetails?.paymentDetails, data.result);

          // Show success message
          showToast("Account created successfully!", "success");

          // Close the modal
          handleToggleUserManualAuth();

          // Check if we're not on the home page, and navigate if needed
          if (window.location.pathname !== "/") {
            router.push("/");
          }
        } else {
          // Show error message
          showToast(
            data.message || "Signup failed. Please try again.",
            "error"
          );
        }
      } else {
        // Handle login
        const response = await fetch(
          `https://td0c8x9qb3.execute-api.us-east-1.amazonaws.com/prod/v1/business/${BUSINESS_ID}/user/login`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email,
              password,
              source: "web",
            }),
          }
        );

        const data = await response.json();

        if (response.ok && data.status === 200) {
          // Store user data in cookies and update store
          setUserAuthResponse(userDetails?.paymentDetails, data.result);

          // Show success toast
          showToast("Login successful!", "success");

          // Close the modal
          handleToggleUserManualAuth();

          // Check if we're not on the home page, and navigate if needed
          if (window.location.pathname !== "/") {
            router.push("/");
          }
        } else {
          // Show error message
          showToast(
            data.message || "Login failed. Please check your credentials.",
            "error"
          );
        }
      }
    } catch (error) {
      console.error(`${activeTab} error:`, error);
      showToast(
        `An error occurred during ${activeTab.toLowerCase()}. Please try again.`,
        "error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (!toggleUserManualAuth) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-10 flex items-start justify-center pt-11 px-8 !m-0">
      <div
        ref={modalRef}
        className="bg-white w-full max-w-md overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-heading uppercase tracking-wide">
              {activeTab === userAuthTypes.SIGN_UP
                ? "Create an account"
                : activeTab === userAuthTypes.RESET_PASSWORD
                ? "Forgot password"
                : "Welcome back!"}
            </h2>

            <Button
              title="Close auth modal"
              variant="ghost"
              className="!p-0"
              onClick={handleToggleUserManualAuth}
            >
              <FontAwesomeIcon icon={faXmark} className="!w-5 !h-5" />
            </Button>
          </div>

          {/* Tabs - hide when in forgot password mode */}
          {activeTab !== userAuthTypes.RESET_PASSWORD && (
            <div className="mb-6">
              <Tabs
                options={[userAuthTypes?.LOGIN, userAuthTypes?.SIGN_UP]}
                value={activeTab}
                size="large"
                tabClassName="!text-base !font-medium"
                onClick={(value) => {
                  setActiveTab(value as string);
                }}
              />
            </div>
          )}

          <form onSubmit={handleSubmit}>
            {/* Full Name field - only for signup */}
            {activeTab === userAuthTypes.SIGN_UP && (
              <div className="mb-4">
                <label
                  htmlFor="name"
                  className="block text-sm font-medium text-gray-600 mb-1"
                >
                  Full Name
                </label>
                <input
                  type="text"
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full p-3 bg-gray-100 border border-gray-200 rounded-md font-poppins"
                  placeholder="Enter your full name"
                  required
                />
              </div>
            )}

            <div className="mb-4">
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-600 mb-1"
              >
                Email address
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full p-3 bg-gray-100 border border-gray-200 rounded-md font-poppins"
                placeholder="Enter your email"
                required
              />
            </div>

            {/* Phone Number field - only for signup */}
            {activeTab === userAuthTypes.SIGN_UP && (
              <div className="mb-4">
                <label
                  htmlFor="phone"
                  className="block text-sm font-medium text-gray-600 mb-1"
                >
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="phone"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  className="w-full p-3 bg-gray-100 border border-gray-200 rounded-md font-poppins"
                  placeholder="Enter your phone number"
                  required
                />
              </div>
            )}

            {/* Password field - not for forgot password */}
            {activeTab !== userAuthTypes.RESET_PASSWORD && (
              <div className="mb-4">
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-600 mb-1"
                >
                  Password
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full p-3 bg-gray-100 border border-gray-200 rounded-md pr-10 font-poppins"
                    placeholder="Enter your password"
                    required
                  />
                  <Button
                    variant="ghost"
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 !p-1"
                    onClick={() => setShowPassword(!showPassword)}
                    aria-label={
                      showPassword ? "Hide password" : "Show password"
                    }
                  >
                    <FontAwesomeIcon
                      icon={showPassword ? faEyeSlash : faEye}
                      size="sm"
                    />
                  </Button>
                </div>
              </div>
            )}

            {/* Forgot Password / Back to Login */}
            {activeTab === userAuthTypes.LOGIN && (
              <div className="flex justify-end mb-6">
                <Button
                  variant="ghost"
                  type="button"
                  className="text-sm text-gray-600 hover:underline !p-0"
                  onClick={() => {
                    setActiveTab(userAuthTypes.RESET_PASSWORD);
                  }}
                >
                  Forgot Password?
                </Button>
              </div>
            )}

            {/* Back to Login - only for forgot password */}
            {activeTab === userAuthTypes.RESET_PASSWORD && (
              <div className="flex justify-end mb-6">
                <Button
                  variant="ghost"
                  type="button"
                  className="text-sm text-gray-600 hover:underline !p-0"
                  onClick={() => {
                    setActiveTab(userAuthTypes.LOGIN);
                  }}
                >
                  Back to Login
                </Button>
              </div>
            )}

            <Button
              variant="primary"
              type="submit"
              disabled={isLoading}
              loading={isLoading}
              className="w-full py-3 bg-black text-white rounded-md font-medium hover:bg-gray-900 transition-colors"
            >
              {isLoading
                ? activeTab === userAuthTypes.SIGN_UP
                  ? "Creating account..."
                  : activeTab === userAuthTypes.RESET_PASSWORD
                  ? "Submitting..."
                  : "Logging in..."
                : activeTab === userAuthTypes.SIGN_UP
                ? "Sign up"
                : activeTab === userAuthTypes.RESET_PASSWORD
                ? "Submit"
                : "Login"}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ManualAuth;
