import { Metadata } from "next";

import ClientView from "./clientView";
import "@/styles/contact.styles.css";
import { PageSEODetails } from "@/types";
import { createMetadata } from "@/lib/utils/helperFunctions";
import { getPageSEOData } from "@/lib/apiConfigs";

// generate metadata for the SEO static page (SSR)
export const generateMetadata = async (): Promise<Metadata> => {
  // function to call SEO API to fetch page SEO data
  const seoDetails: PageSEODetails = await getPageSEOData("contact");

  // function call to create Next.js metadata from SEO data
  return createMetadata(seoDetails);
};

// contact us page component
const ContactUs: React.FC = async () => {
  // ==============================|| API calls ||============================== //

  // function to call SEO API to fetch page h1
  const seoDetails: PageSEODetails = await getPageSEOData("contact");

  // ==============================|| UI ||============================== //

  return (
    <main>
      {/* hidden h1 for SEO */}
      <h1 className="sr-only">{seoDetails?.h1}</h1>

      <figure className="contact_ab">
        <img
          src="https://static.tossdown.com/site/73d10b9e-dead-485a-861a-77be049281de.webp"
          alt="Contact banner"
        />
      </figure>

      <section className="contact_section_main">
        <section className="contact_section_one">
          <div className="inner-custom-heading">
            <small>
              <strong></strong> <span></span>
              <span></span>
              <span></span>
            </small>

            <h3>CONTACT US</h3>

            <small>
              <span></span>
              <span></span>
              <span></span>
              <strong></strong>
            </small>
          </div>

          <img
            className="left-img"
            src="https://static.tossdown.com/site/22989328-6178-4db5-a555-719d5f0a5c8c.webp"
            alt="Left decoration"
          />

          <ClientView />

          <img
            className="right-img"
            src="https://static.tossdown.com/site/0b1691a5-6bef-46ee-9358-c26d39f4dc74.webp"
            alt="Right decoration"
          />
        </section>
      </section>
    </main>
  );
};

export default ContactUs;
