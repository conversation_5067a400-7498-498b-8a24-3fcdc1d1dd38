import { getPageSeo } from "@/services/seo-service";
// Common SEO/text helpers
export function stripHtml(input?: string): string {
  if (!input) return "";
  return input
    .replace(/<[^>]+>/g, " ")
    .replace(/&nbsp;/g, " ")
    .replace(/\s+/g, " ")
    .trim();
}

// Format SEO description: cap at 150 chars, then extend to end of sentence if a period follows shortly
export function formatSeoDescription(
  input?: string,
  hardLimit: number = 150
): string {
  const text = (input || "").trim();
  if (!text) return "";

  if (text.length <= hardLimit) return text;

  // Take the first hardLimit chars
  const head = text.slice(0, hardLimit);

  // If head already ends at a period, return it as-is
  if (/\.$/.test(head)) return head;

  // Otherwise, look ahead in the remainder for the next period
  const rest = text.slice(hardLimit);
  const periodIdx = rest.indexOf(".");

  if (periodIdx >= 0) {
    // Append up to and including the period
    return (head + rest.slice(0, periodIdx + 1)).trim();
  }

  // No period found; just return the head
  return head.trim();
}

/**
 * Gets the H1 tag content for a specific page
 */
export async function getPageH1(pageName: string): Promise<string | null> {
  try {
    const pageSeo = await getPageSeo(pageName);

    if (!pageSeo || !pageSeo.seo.h1) {
      return null;
    }

    return pageSeo.seo.h1;
  } catch (error) {
    console.error(`Error getting H1 for page ${pageName}:`, error);
    return null;
  }
}
