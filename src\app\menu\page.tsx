import { Metadata } from "next";
import { cookies } from "next/headers";

import { <PERSON><PERSON><PERSON><PERSON>, Menu as MenuComponent } from "@/app/components";

import { CookiesDetails, PageSEODetails } from "@/types";
import { getPageSEOData, callProductsAPI } from "@/lib/apiConfigs";
import { createMetadata, getServerCookies } from "@/lib/utils/helperFunctions";
import { cookiesKey } from "@/lib/utils/constants";

// generate metadata for the menu page (SSR)
export const generateMetadata = async (): Promise<Metadata> => {
  // function to call SEO API to fetch page SEO data
  const seoDetails: PageSEODetails = await getPageSEOData("menu");

  // function call to create Next.js metadata from SEO data
  return createMetadata(seoDetails);
};

// menu page component
const Menu = async () => {
  // ==============================|| API calls ||============================== //

  // function to call SEO API to fetch page h1
  const seoDetails: PageSEODetails = await getPageSEOData("menu");

  // get cookies from Next.js server-side cookies
  const cookieStore = await cookies();

  // get selected branch ID from cookies
  const cookieValue: string | undefined = cookieStore?.get(
    cookiesKey?.cart
  )?.value;

  // function call to parse cookies
  const cookieDetails: CookiesDetails | null = getServerCookies(cookieValue);

  // function to call products API
  const { data, error } = await callProductsAPI(cookieDetails?.branchId);

  // ==============================|| UI ||============================== //

  return (
    <>
      <main>
        <section className="static_container mx-auto py-8 md:py-12">
          {/* hidden h1 for SEO */}
          <h1 className="sr-only">{seoDetails?.h1}</h1>

          {/* page heading */}
          <div className="flex items-center justify-center w-full pb-8">
            <h2 className="font-heading text-[46px] leading-[50px] font-normal text-left max-[992px]:text-[36px] max-[992px]:leading-[42px]">
              ORDERING MENU
            </h2>
          </div>

          {/* error message */}
          {!!error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto mb-8">
              <p className="text-red-600 text-center">
                {
                  "Unable to load menu items at the moment. Please try refreshing the page."
                }
              </p>
            </div>
          )}

          {/* menu */}
          {!error && <MenuComponent products={data} enableCart={true} />}
        </section>
      </main>

      {/* meal box selector */}
      <MealBox />
    </>
  );
};

export default Menu;
