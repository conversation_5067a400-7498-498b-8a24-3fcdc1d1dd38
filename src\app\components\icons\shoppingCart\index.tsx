import { IconProps } from "../../index.types";

const ShoppingCart = ({ className = "" }: IconProps) => {
  return (
    <svg
      viewBox="0 0 23 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`${className}`}
    >
      <g clipPath="url(#clip0_1839_401)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M1.31236 2.42731H3.50607L4.10165 4.58731C4.10647 4.60442 4.11171 4.62143 4.11736 4.63831L5.6385 11.9058C5.64265 11.9451 5.64895 11.9841 5.65736 12.0228L6.31736 15.1728V15.1773C6.40793 15.6008 6.649 15.9815 6.99979 16.2548C7.35058 16.5281 7.78953 16.6774 8.24236 16.6773H17.8124C18.1249 16.6773 18.4247 16.5588 18.6457 16.3478C18.8668 16.1368 18.9909 15.8507 18.9909 15.5523C18.9909 15.2539 18.8668 14.9678 18.6457 14.7568C18.4247 14.5458 18.1249 14.4273 17.8124 14.4273H8.5645L8.17165 12.5523H19.1669C19.2219 12.5523 19.3791 12.5568 19.5236 12.5343C19.7828 12.4925 20.0271 12.3898 20.2344 12.2356C20.4418 12.0814 20.6058 11.8805 20.7116 11.6508C20.7965 11.4648 20.8389 11.2293 20.8531 11.1513L20.8578 11.1303L22.1008 5.33731L22.1039 5.32231C22.145 5.10654 22.1361 4.88479 22.0778 4.67267C22.0195 4.46055 21.9132 4.26319 21.7664 4.09448C21.6196 3.92577 21.4359 3.7898 21.2281 3.69613C21.0204 3.60247 20.7937 3.55337 20.5639 3.55231H6.25293L5.72807 1.63981C5.62995 1.22448 5.38666 0.853396 5.03823 0.587593C4.68979 0.321789 4.25697 0.177094 3.81093 0.177307H1.31236C0.999784 0.177307 0.70001 0.295834 0.478985 0.506812C0.25796 0.717791 0.133789 1.00394 0.133789 1.30231C0.133789 1.60068 0.25796 1.88682 0.478985 2.0978C0.70001 2.30878 0.999784 2.42731 1.31236 2.42731ZM18.4095 19.3773C18.4095 18.9202 18.2193 18.4818 17.8807 18.1586C17.5421 17.8354 17.0828 17.6538 16.6039 17.6538C16.1251 17.6538 15.6658 17.8354 15.3272 18.1586C14.9886 18.4818 14.7984 18.9202 14.7984 19.3773C14.8122 19.8254 15.0084 20.2507 15.3454 20.563C15.6823 20.8753 16.1335 21.05 16.6031 21.05C17.0728 21.05 17.524 20.8753 17.8609 20.563C18.1979 20.2507 18.3941 19.8254 18.4079 19.3773H18.4095ZM8.74522 17.6553C8.98671 17.6485 9.22717 17.688 9.45236 17.7715C9.67756 17.8551 9.88291 17.9809 10.0562 18.1415C10.2296 18.3022 10.3674 18.4944 10.4615 18.7068C10.5556 18.9192 10.604 19.1474 10.604 19.3781C10.604 19.6087 10.5556 19.8369 10.4615 20.0493C10.3674 20.2617 10.2296 20.454 10.0562 20.6146C9.88291 20.7752 9.67756 20.9011 9.45236 20.9846C9.22717 21.0681 8.98671 21.1076 8.74522 21.1008C8.27578 21.0876 7.83021 20.9003 7.50305 20.5786C7.1759 20.257 6.99291 19.8264 6.99291 19.3781C6.99291 18.9298 7.1759 18.4991 7.50305 18.1775C7.83021 17.8558 8.27578 17.6685 8.74522 17.6553Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1839_401">
          <rect
            width="22"
            height="21"
            fill="white"
            transform="translate(0.133789 0.177307)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default ShoppingCart;
