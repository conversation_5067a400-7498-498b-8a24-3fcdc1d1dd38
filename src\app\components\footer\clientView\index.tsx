"use client";

import { useEffect } from "react";
import Link from "next/link";

import BusinessContact from "@/app/components/businessContact";
import { useSelector } from "@/lib/store";

const ClientView = () => {
  const { email, contact } = useSelector((state) => state.business);

  // ==============================|| useEffect hook ||============================== //

  // useEffect hook to implement the accordion functionality for mobile
  useEffect(() => {
    if (typeof document === "undefined") {
      return;
    }

    const accordion: HTMLCollectionOf<Element> =
      document.getElementsByClassName("accordion");

    for (let count = 0; count < accordion?.length; count++) {
      accordion?.[count]?.addEventListener("click", handleAccordionClick);
    }

    // cleanup function
    return () => {
      for (let count = 0; count < accordion.length; count++) {
        accordion?.[count]?.removeEventListener("click", handleAccordionClick);
      }
    };
  }, []);

  // ==============================|| handler functions ||============================== //

  // function to toggle the accordion classes
  const handleAccordionClick = (event: Event): void => {
    const element = event?.currentTarget as HTMLElement;

    if (!element) {
      return;
    }

    element?.classList?.toggle("active");

    const panel = element?.nextElementSibling as HTMLElement | null;

    if (panel) {
      panel.style.display =
        panel?.style?.display === "block" ? "none" : "block";
    }
  };

  // ==============================|| UI ||============================== //

  return (
    <div className="modcontent panel accordion modtitle">
      <ul className="footer-contact-list ">
        <li>
          <Link href={`tel:${contact}`}>
            <BusinessContact type="phone" />
          </Link>
        </li>

        <li>
          <Link href={`mailto:${email}`}>
            <BusinessContact />
          </Link>
        </li>
      </ul>
    </div>
  );
};

export default ClientView;
