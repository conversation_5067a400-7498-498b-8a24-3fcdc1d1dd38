"use client";

import { formatPhoneNumber } from "@/lib/utils/helperFunctions";
import { useSelector } from "@/lib/store";
import { BusinessContactProps } from "../index.types";

const BusinessContact: React.FC<BusinessContactProps> = ({
  type = "email",
}) => {
  const { email, contact } = useSelector((state) => state.business);

  if (type === "phone") {
    return <>{formatPhoneNumber(contact)}</>;
  }

  return <>{email}</>;
};

export default BusinessContact;
