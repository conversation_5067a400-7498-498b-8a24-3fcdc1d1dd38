import { createSlice } from "@reduxjs/toolkit";

import { ScheduleState } from "@/app/components/index.types";
import {
  AddressDetails,
  Cart,
  Locations,
  OrderTypes,
  UserDetails,
} from "@/types";
import { initialSchedule, initialUserDetails } from "@/lib/utils/constants";

// ==============================|| slice type ||============================== //

export interface OrderState {
  orderTypes: string[];

  // persisted keys
  cartId: string;
  location: Locations | null;
  boxSize: number;
  cartDetails: Cart;

  orderType: OrderTypes | string;
  scheduleType: string;
  schedule: ScheduleState;

  userDetails: UserDetails;
  addressDetails: AddressDetails;

  // cart drawer state (not persisted)
  cartDrawer: boolean;

  [key: string]:
    | string
    | string[]
    | number
    | boolean
    | Locations
    | UserDetails
    | AddressDetails
    | ScheduleState
    | Cart
    | null;
}

// ==============================|| initial state ||============================== //

const initialState: OrderState = {
  orderTypes: [],

  // persisted keys
  cartId: "",
  location: null,
  boxSize: 0,
  cartDetails: { subTotal: "0", itemsCount: 0, items: {} },

  orderType: "",
  scheduleType: "",
  schedule: initialSchedule,

  userDetails: initialUserDetails,

  addressDetails: {
    address: "",
    street: "",
    area: "",
    city: "",
    country: "",
    postalCode: "",
    location: null,
  },

  // cart drawer state (not persisted)
  cartDrawer: false,
};

// ==============================|| order slice ||============================== //

const Order = createSlice({
  name: "order",
  initialState,
  reducers: {
    updateOrderStore(state, action) {
      state[action.payload.type] = action.payload.value;
    },
    bulkUpdateOrderStore(state, action) {
      return { ...state, ...action.payload };
    },
    toggleCartDrawer(state) {
      state.cartDrawer = !state.cartDrawer;
    },
  },
});

export default Order.reducer;

export const { updateOrderStore, bulkUpdateOrderStore, toggleCartDrawer } =
  Order.actions;
