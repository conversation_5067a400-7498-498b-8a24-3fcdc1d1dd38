.page_top_padding {
  display: block;
  width: 100%;

  margin-top: 30px;
  position: relative;
}

.static_breadcrums_parent {
  display: block;
  width: 100%;
  padding: 0px 15px;
  margin-bottom: 32px;
}

.static_breadcrums {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.static_breadcrums a {
  color: #000;
  font-family: Poppins;
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  text-transform: uppercase;
}

.static_breadcrums span {
  color: #000;
  font-family: Poppins;
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  text-transform: uppercase;
}

.custom_sec_11_parent {
  display: block;
  width: 100%;
  padding: 0px 15px;
  float: left;
  margin-bottom: 72px;
  position: relative;
}

.custom_sec_11_data {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 30px 30px;
  width: 100%;
  align-items: center;
  height: 100%;
}

.custom_sec_11_data figure {
  margin-bottom: 0px;
  width: 100%;
  height: 100%;
  position: relative;
}

.custom_sec_11_data iframe {
  margin-bottom: 0px;
  width: 100%;
  height: 100%;
  position: relative;
}

.custom_sec_11_data figure img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.custom_sec_11_data_body {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
}

.custom_sec_11_data_heading {
  display: grid;
  width: 100%;
}

.custom_sec_11_data_heading h3,
.custom_sec_11_data_heading h2 {
  padding-bottom: 20px;
  margin: 0px;
  font-family: Anton;
  font-weight: 400;
  font-size: 40px;
  line-height: 113.99999999999999%;
  text-transform: uppercase;
  color: #000;
}

.custom_sec_11_data_heading.app_data h4 {
  padding-bottom: 20px;
  margin: 0px;
  font-family: Josefin Sans;
  font-weight: 700;
  font-size: 36px;
  line-height: 100%;
  text-transform: uppercase;
  color: #000;
  max-width: 450px;
}

.custom_sec_11_data_heading h3 a,
.custom_sec_11_data_heading h2 a,
.custom_sec_11_data_heading h4 a {
  color: #000 !important;
  font-weight: bold;
}

.custom_sec_11_des {
  display: flex;
  flex-direction: column;
  padding-bottom: 20px;
}

.custom_sec_11_data_heading p {
  font-family: Poppins;
  font-weight: 400;
  font-size: 16px;
  line-height: 150%;
  color: #000;
}

.custom_sec_11_data_heading p a {
  color: #000 !important;
  font-weight: 700;
}

.custom_sec_11_btn {
  width: 100%;
  display: flex;
  gap: 10px;
}

.custom_sec_11_btn.app_btn {
  flex-direction: column;
}

.custom_sec_11_btn a {
  width: 188px;
  height: 53px;
  border-radius: 68px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000;

  font-family: Poppins;
  font-weight: 700;
  font-size: 14px;
  line-height: 100%;
  color: #fff !important;
  border: 1px solid #000;
  text-transform: uppercase;
}

.custom_sec_11_btn.app_btn {
  background: transparent !important;
  gap: 25px;
}

.custom_sec_11_btn a:hover {
  background: #000 !important;
  color: #fff !important;
}

.custom_sec_11_btn.app_btn a {
  width: unset;
  background: transparent !important;
  border: unset !important;
  justify-content: flex-start !important;
}

@media (max-width: 1024px) {
  .custom_sec_11_data {
    grid-template-columns: 1.3fr 1fr;
  }

  .custom_sec_11_parent {
    margin-bottom: 40px;
  }

  .custom_sec_11_data_heading h3,
  .custom_sec_11_data_heading h2 {
    font-size: 34px;
  }

  .custom_sec_11_data_heading.app_data h4 {
    max-width: 310px;
    font-size: 24px;
  }

  .custom_sec_11_btn a {
    width: 170px;
    height: 44px;
  }

  .custom_sec_11_data_heading p {
    font-size: 14px;
  }

  .custom_sec_11_btn.app_btn a {
    width: 154px !important;
  }

  .custom_sec_11_btn.app_btn a img {
    width: 100%;
  }

  .custom_sec_11_btn.app_btn {
    gap: 13px;
  }
}

@media (max-width: 768px) {
  .custom_sec_11_data {
    display: flex;
    flex-direction: column-reverse;
    gap: 0px;
  }

  .custom_sec_11_data.custom_sec_11_reverse {
    flex-direction: column;
  }

  .custom_sec_11_data_body {
    padding-bottom: 30px;
  }

  .custom_sec_11_data figure {
    display: flex;
    justify-content: center;
  }

  .custom_sec_11_data_heading h3,
  .custom_sec_11_data_heading h2 {
    font-size: 24px;
  }
}

@media (max-width: 540px) {
}

.cus_sec2_parent {
  display: inline-block;
  width: 100%;
  padding: 0px 15px;
  margin-bottom: 60px;
}

.cus_pro_box {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.cus_pro_box_des p {
  font-family: Poppins;
  font-weight: 400;
  font-style: italic;
  font-size: 16px;
  line-height: 150%;
}

.cus_pro_box_des p a {
  font-weight: 700;
}

.cus_pro_box h2 {
  padding-bottom: 8px;

  margin: 0px;
  font-family: Anton;
  font-weight: 400;
  font-size: 40px;
  line-height: 113.99999999999999%;
  letter-spacing: 0%;
  text-transform: uppercase;

  color: #000;
}

.cus_pro_listing {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -20px;
}

.cus_single_pro {
  flex: 0 0 33.3333%;
  max-width: 33.3333%;
  padding: 15px 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.cus_sec2_praent_box {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.cus_single_pro_data_parent {
  height: 100%;

  transition: 0.3s ease-in-out;
}

.cus_single_pro_img img {
  object-fit: cover;
  aspect-ratio: 1 / 1;
  background: #f9f7f6;
}

.cus_single_pro_data {
  padding-top: 16px;
  display: flex;
  flex-direction: column;
}

.cus_single_pro_data h3 {
  color: #000;
  margin-bottom: 10px;
  display: -webkit-box !important;
  width: 100%;
  overflow: hidden;

  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  width: 100%;
  max-width: 100%;
  font-family: Poppins;
  font-weight: 700;
  font-size: 16px;
  line-height: 150%;
  color: #000000;
  height: 20px;
}

.cus_single_pro_data h3 a {
  color: #000 !important;
}

.cus_single_pro_data p {
  font-family: Poppins;
  font-weight: 400;
  font-size: 16px;
  line-height: 150%;
  display: flex;
  flex-direction: column;
  gap: 5px;

  display: -webkit-box;
  -webkit-line-clamp: 2;

  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cus_single_pro_data p.expanded {
  display: block;
  -webkit-line-clamp: unset;
  overflow: visible;
  cursor: pointer;
}

.cus_single_pro_data .order-btn {
  color: #000;
  background: transparent !important;
  text-align: left;
  border: unset !important;
  margin-top: 10px;
  font-weight: bold;
  font-size: 16px;
}

.skeleton {
  background: #f0f0f0;
  border-radius: 4px;
  position: relative;
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    background-color: #f0f0f0;
  }

  50% {
    background-color: #e0e0e0;
  }

  100% {
    background-color: #f0f0f0;
  }
}

@media (max-width: 1024px) {
  .cus_sec2_parent {
    margin-bottom: 30px;
  }

  .cus_pro_listing {
    margin: 0 -7px;
  }

  .cus_single_pro {
    padding: 7px 7px;
  }

  .cus_pro_box h2 {
    font-size: 32px;
  }
}

@media (max-width: 768px) {
  .cus_single_pro {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .cus_pro_box h2 {
    font-size: 24px;
  }
}

@media (max-width: 540px) {
  .cus_single_pro_data p {
    font-size: 14px;
  }

  .cus_single_pro_data .order-btn {
    font-size: 14px;
  }

  .cus_single_pro_data h3 {
    -webkit-line-clamp: 2;

    height: 44px;
  }
}
.cus_sec3_parent {
  display: inline-block;
  width: 100%;
  padding: 0px 15px;
  margin-bottom: 72px;
}

.cus_sec3_box {
  display: grid;
  gap: 50px 0px;
  width: 100%;
}

.cus_sec3_data {
  display: grid;
  gap: 20px 0px;
  width: 100%;
}

.cus_sec3_data h2 {
  margin: 0px;
  font-family: Josefin Sans;
  font-weight: 700;
  font-size: 36px;
  line-height: 100%;
  letter-spacing: 0%;
  text-transform: uppercase;
  color: #000;
}

.cus_sec3_inner_data {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.cus_sec3_inner_data h4 {
  font-family: Poppins;
  font-weight: 700;
  font-size: 16px;
  line-height: 150%;
  color: #000;
}

.cus_sec3_data h3 {
  margin: 0px;
  font-family: Anton;
  font-weight: 400;
  font-size: 40px;
  line-height: 113.99999999999999%;
  letter-spacing: 0%;
  text-transform: uppercase;
  color: #000;
}

.cus_sec3_data h2 {
  margin: 0px;
  font-family: Anton;
  font-weight: 400;
  font-size: 40px;
  line-height: 113.99999999999999%;
  letter-spacing: 0%;
  text-transform: uppercase;
  color: #000;
}

.cus_sec3_data h3 a {
  color: #000 !important;
}

.cus_sec3_data h2 a {
  color: #000 !important;
}

.cus_sec3_des {
  display: flex;
  flex-direction: column;
}

.cus_sec3_data p {
  font-family: Poppins;
  font-weight: 400;
  font-size: 16px;
  line-height: 150%;
  color: #000;
}

.cus_sec3_data p a {
  color: #000 !important;
  font-weight: bold;
}

.cus_sec3_img_grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px 86px;
}

.cus_sec3_img_grid img {
  width: 100%;
  object-fit: cover;
}

.cus_sec3_data ul {
  display: grid;
  gap: 10px 0px;
  width: 100%;
  padding: 0px 0px 0px 17px;
}

.cus_sec3_data ul li {
  font-family: Poppins;
  font-weight: 400;
  font-size: 16px;
  line-height: 150%;
  color: #000;
  list-style: square;
}

.cus_sec3_data ul li::marker {
  color: #000 !important;
}
.cus_sec3_data ul li a {
  font-weight: bold;
}
.cus_sec3_data ol {
  display: grid;
  gap: 10px 0px;
  width: 100%;
  padding: 0px 0px 0px 17px;
}

.cus_sec3_data ol li {
  font-family: Poppins;
  font-weight: 400;
  font-size: 16px;
  line-height: 150%;

  color: #000;
}
.cus_sec3_data ol li a {
  font-weight: bold;
}
@media (max-width: 1024px) {
  .cus_sec3_parent {
    margin-bottom: 40px;
  }

  .cus_sec3_data h3 {
    font-size: 24px;
  }

  .cus_sec3_data h2 {
    font-size: 24px;
  }

  .cus_sec3_data p {
    font-size: 14px;
  }

  .cus_sec3_box {
    gap: 30px 0px;
  }

  .cus_sec3_inner_data {
    gap: 10px;
  }

  .cus_sec3_img_grid {
    gap: 30px 40px;
  }
}

@media (max-width: 768px) {
  .cus_sec3_img_grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 540px) {
  .cus_sec3_data h2 {
    font-size: 20px;
    line-height: 30px;
  }

  .cus_sec3_box {
    gap: 20px 0px;
  }
}
.cus_sec4_parent {
  display: inline-block;
  width: 100%;
  padding: 30px 15px;
  margin-bottom: 30px;
  background: #ffc100;
}

.cus_sec4_box {
  display: flex;

  width: 100%;
}

.cus_sec4_data {
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
  gap: 20px 0px;
  width: 100%;
  flex-direction: column;
}

.cus_sec4_data h4 {
  margin: 0px;
  font-family: Anton;
  font-weight: 400;
  font-size: 50px;
  line-height: 100%;
  text-transform: uppercase;

  text-align: center;
  text-transform: uppercase;

  color: #000;
}

@media (max-width: 1024px) {
  .cus_sec4_parent {
    padding: 20px 15px;
  }

  .cus_sec4_data h4 {
    font-size: 24px;
  }
}

@media (max-width: 540px) {
}
.location_sec {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 17px;
}

.single_loc {
  background: #141414;
  padding: 20px 17px;
  display: flex;
  flex-direction: column;
}

.single_loc h4 {
  font-family: Josefin Sans;
  font-weight: 700;
  font-size: 16px;
  line-height: 20px;
  text-transform: uppercase;
  color: #fff;
  padding-bottom: 10px;
}

.single_loc p {
  font-family: Josefin Sans;
  font-weight: 400;
  font-size: 16px;
  line-height: 20px;
  color: #b8b8b8;
}

@media (max-width: 768px) {
  .location_sec {
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }

  .single_loc {
    padding: 15px 12px;
  }

  .single_loc p {
    font-size: 14px;
  }

  .single_loc h4 {
    font-size: 14px;
  }
}
.faq_sec {
  display: grid;
  gap: 20px;
}

.single_faq {
  display: flex;
  flex-direction: column;
  background: #fff1c5;
  padding: 20px;
  gap: 10px;
}

.single_faq h4 {
  font-family: Poppins;
  font-weight: 700;
  font-size: 16px;
  line-height: 20px;

  color: #000;
}

.single_faq p {
  font-family: Poppins;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #000;
}

.single_faq p a {
  color: #000 !important;
  font-weight: 700;
}

@media (max-width: 768px) {
  .single_faq p {
    font-size: 14px;
  }
}
.custom_sec_12_parent {
  display: inline-block;
  width: 100%;
  margin-bottom: 40px;
  padding: 0px 15px;
}

.custom_sec_12_data {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.custom_sec_12_data h2 {
  font-family: Anton;
  font-weight: 400;
  font-size: 40px;
  line-height: 113.99999999999999%;
  letter-spacing: 0%;
  text-transform: uppercase;
  color: #000;
  margin-bottom: 20px;
}

.custom_sec_12_btn {
  width: 100%;
  display: flex;
  gap: 10px;
}

.custom_sec_12_btn a {
  font-family: Poppins;
  font-weight: 700;
  font-size: 14px;
  line-height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #000;
  width: 188px;
  height: 53px;
  border: 1px solid #000;
  border-radius: 60px;
}

.custom_sec_12_btn a.active_btn {
  background: #000;
  color: #fff;
}

@media (max-width: 1024px) {
  .custom_sec_12_data h2 {
    font-size: 32px !important;
    margin-bottom: 25px;
  }

  .custom_sec_12_data p {
    font-size: 14px;
    line-height: 26px;
  }

  .custom_sec_12_btn a {
    width: 170px;
    height: 44px;
  }
}

@media (max-width: 768px) {
  .custom_sec_12_data h2 {
    font-size: 24px !important;

    margin-bottom: 20px;
  }
}
