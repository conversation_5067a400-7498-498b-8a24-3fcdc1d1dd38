name: ezeats
services:
  - name: web
    bitbucket:
      repo_clone_url: git clone https://<PERSON><PERSON><PERSON>@bitbucket.org/tossdownInc/ezeats.git
      branch: development
      deploy_on_push: true
    build_command: "npm install && npm run build:prod"
    run_command: "npm run start:prod"
    environment_slug: node-js
    instance_size_slug: basic-xxs
    instance_count: 1
    envs:
      - key: GOOGLE_MAPS_API_KEY
        value: ${GOOGLE_MAPS_API_KEY}
      - key: BUSINESS_ID
        value: ${BUSINESS_ID}
      - key: BRANCH_ID
        value: ${BRANCH_ID}
    http_port: 3000
    health_check:
      http_path: /
    cors:
      allow_origins:
        - https://*.tossdown.com
