"use client";

import React, { useEffect, useMemo } from "react";

import { BusinessInfo } from "@/types";
import {
  removeDOMElements,
  cleanupMUIElements,
  passBusinessInfo,
  integrateCheckout,
} from "@/lib/utils/helperFunctions";
import { checkoutScripts } from "@/lib/utils/constants";
import { useSelector } from "@/lib/store";

const ClientView: React.FC = () => {
  const orderDetails = useSelector((state) => state.order);

  // business info to be passed to checkout
  const businessInfo: BusinessInfo = useMemo<BusinessInfo>(
    () => ({
      cartId: orderDetails?.cartId || "",
      businessId: process.env.NEXT_PUBLIC_BUSINESS_ID || "",
      branchId: orderDetails?.location?.id || "",
      orderType: orderDetails?.orderType || "",
      addressDetails: {
        address: orderDetails?.addressDetails?.address || "",
        city: orderDetails?.addressDetails?.city || "",
        apartment: "",
        area: orderDetails?.addressDetails?.area || "",
        country: orderDetails?.addressDetails?.country || "",
        postalCode: orderDetails?.addressDetails?.postalCode || "",
      },
      userLocation: {
        lat: orderDetails?.addressDetails?.location?.lat?.toString() || "0",
        lng: orderDetails?.addressDetails?.location?.lng?.toString() || "0",
      },
      subscriptionType: orderDetails?.scheduleType || "",
      orderSchedule: {
        date: orderDetails?.schedule?.date?.value || "",
        time: orderDetails?.schedule?.time || "",
      },
      disableAddressUpdate: true,
      confirmationRedirectURL: "",
      source: "ordrz",
    }),
    [orderDetails]
  );

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function call to load checkout scripts
    integrateCheckout();

    // cleanup on unmount
    return () => {
      // remove checkout scripts
      removeDOMElements(Object.values(checkoutScripts) || []);

      // remove MUI elements
      cleanupMUIElements();
    };
  }, []);

  useEffect(() => {
    // function call to pass business info to checkout via DOM
    passBusinessInfo(businessInfo);
  }, [businessInfo]);

  // ==============================|| UI ||============================== //

  // div to mount checkout with id "root" defined in checkout
  return <div id="root"></div>;
};

export default ClientView;
