import React from "react";

import { ProductCard } from "@/app/components";

import { MenuProps } from "../index.types";
import { ProductsList } from "@/types";

const Menu: React.FC<MenuProps> = ({ products = [], enableCart = false }) => {
  return (
    <section>
      <div className="static_container mx-auto px-2.5 lg:px-0">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 lg:gap-5">
          {/* display products via product card */}
          {products?.map((product: ProductsList) => (
            <ProductCard
              key={product?.id}
              product={product}
              enableCart={enableCart}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Menu;
