import { IconDefinition } from "@fortawesome/fontawesome-svg-core";
import { IconProps } from "@/app/components/index.types";

// ==============================|| generic types ||============================== //

export type GenericMap = {
  [key: string]: string | number | GenericMap | unknown;
};

export interface RequestConfig {
  baseURL?: string;
  url: string;
  method: string;
  params?: unknown;
  data?: unknown;
  timeout: number;
}

export interface RequestHelperResponse {
  data: ProductsList[];
  error: unknown;
}

export interface NextResponseData {
  data:
    | ProductsList[]
    | BusinessDetails
    | GetUserDistanceResponse
    | GetPickupSlotsResponse
    | Cart
    | null;
  meta: unknown;
  error: unknown;
}

export interface RequestState {
  loading: boolean | string;
  error: string;
}

export interface RequestStateData<T> extends RequestState {
  data: T;
}

export interface DropDownList {
  label: string;
  value: string;
}

// ==============================|| internal types ||============================== //

export interface CookiesDetails {
  branchId: string;
}

export interface NavMenuItem {
  label: string;
  url: string;
  icon: IconDefinition;
}

export enum ScheduleTypes {
  LATER = "later",
  WEEKLY = "weekly",
  BIWEEKLY = "bi-weekly",
}

export interface ScheduleTypesOptions {
  label: string;
  value: ScheduleTypes;
  icon: ({ className }: IconProps) => React.JSX.Element;
}

export interface UserDetails {
  name: string;
  email: string;
  phone: string;
  userId: string;
  tdUserId: string;
  authToken: string;
  renewAuthToken: string;
  paymentDetails: PaymentDetailsType;
}

export interface PaymentDetailsType {
  stripe: StripeDetails;
}

export interface StripeDetails {
  stripeCustomerId: string;
  selectedCardId: string;
  saveCard: boolean;
}

export interface AddressDetails {
  address: string;
  street: string;
  area: string;
  city: string;
  country: string;
  postalCode: string;
  location: google.maps.LatLngLiteral | null;
}

export enum OrderTypes {
  DELIVERY = "delivery",
  PICKUP = "pickup",
}

export interface BusinessDetails {
  name: string;
  currency: string;
  minSpend: number;
  inventory: number;
  contact: string;
  email: string;
  decimalPlaces: number;
  addressKey: string;
  locations: Locations[];

  [key: string]: string | number | Locations[];
}

export interface Locations {
  id: string;
  address: string;
  location: string;
  city: string;
  country: string;
  delivery: number;
  pickup: number;
  email: string;
  lat: string;
  lng: string;
  timeZone: string;
  deliverySettings: string;
}

export interface ProductsList {
  id: string;
  name: string;
  price: string;
  currency: string;
  image: string;
  description: string;
  categoryId: string;
  category: string;
  inStock: boolean;
  featured: boolean;
  rating: string;
  attributes: string;
  inventory: string;
}

export enum ChargesTypes {
  GEO_RANGE = "geo_range",
}

export interface DeliveryCharges {
  charges_type: ChargesTypes.GEO_RANGE;
  charges_details: ChargesDetails;
}

export interface ChargesDetails {
  charges: DeliveryRanges[];
  shipping_service: unknown;
}

export interface DeliveryRanges {
  max_distance: string;
  min_distance: string;
  delivery_charges: string;
  amount_based_discounts: unknown;
}

export enum CartActions {
  ADD = "add",
  UPDATE = "sub",
  REMOVE = "delete",
}

export interface BusinessInfo {
  cartId: string;
  businessId: string;
  branchId: string;
  orderType: string;
  addressDetails: {
    address: string;
    city: string;
    apartment: string;
    area: string;
    country: string;
    postalCode: string;
  };
  userLocation: { lat: string; lng: string };
  subscriptionType: string;
  orderSchedule: { date: string; time: string };
  disableAddressUpdate?: boolean;
  confirmationRedirectURL?: string;
  source: string;
}

export interface Testimonials {
  id: number;
  name: string;
  image: string;
  review: string;
  rating: number;
  role: string;
  location: string;
}

// ==============================|| API response types ||============================== //

export interface RootXML {
  "?xml": {
    "@_version": number;
    "@_encoding": string;
  };
  results: { page: PageSEO[] };
}

export interface PageSEO {
  seo: PageSEODetails;
  generated: PageSEODetails;
  "@_name": string;
}

export interface PageSEODetails {
  pagetitle: string;
  h1: string;
  desc: string;
  keywords: string;
}

export interface ProductsItem {
  comment: string;
  quantity: string;
  slots: null;
  time_bound_availability: number;
  discount_expired: string;
  category_slug: string | null;
  category_seo?: ProductSEO;
  brand_slug: string | null;
  max_distance: number;
  available_in_distance: string;
  canada_post_distance: number;
  is_parent: boolean;
  is_grouped: boolean;
  grouped_products: unknown[];
  attributes: GenericMap[];
  name: string;
  menu_item_id: string;
  menu_cat_id: string;
  menu_cat_image: string;
  menu_cat_image_thumbnail: string;
  menu_cat_sku: string;
  nutritions: string;
  price: string;
  currency: string;
  desc: string;
  category: string;
  image: string;
  large_image: string;
  options: unknown[];
  discount_display: string;
  discount_value: string;
  discount: string;
  discount_expiry: string;
  discount_start_at: string;
  tax: string;
  weight: number;
  calculated_weight: number;
  sku: string;
  status: string;
  status_note: string;
  brand: unknown[];
  item_brand: ItemBrand[];
  carton: string;
  pallet: string;
  carton_price: string;
  pallet_price: string;
  product_code: string;
  upc: string;
  suggestions: unknown[];
  images: ItemImage[];
  calculated_price: number;
  branches: string;
  is_selected: boolean;
  unit_price: string;
  item_weight: string;
  allow_note: string;
  note: string;
  featured_section: string;
  featured: string;
  order_limit: string;
  price_per: string;
  inv_limit: string;
  weight_value: string;
  weight_unit: string;
  branch_na: string[];
  min_qty: string;
  display_source: string;
  seo?: Record<string, unknown>;
  category_id?: string;
  product_rating?: string;
  total?: number;
  item_level_grand_total?: number | string;
  item_level_discount_value?: number | string;
  coupon_discount?: number | string;
  coupon_discount_value?: number | string;
  item_level_tax_value?: number | string;
  label?: string;
}

export interface ProductSEO {
  title: string;
  h1: string;
  description: string;
  keywords: string;
}

export interface ItemImage {
  image_thumbnail: string;
  image: string;
  image_id: null | string;
  image_position: null | string;
}

export interface ItemBrand {
  brand_id: string;
  brand_name: string;
}

export interface BusinessDetailsResponse {
  name: string;
  logo: string;
  currencycode: string;
  aws_flag: number;
  time_zone: string;
  username: string;
  discount: number;
  tax: number;
  minimum_spend: number;
  inventory: number;
  delivery_charges_tax: number;
  tax_type: number;
  settings: {
    cod: number;
    coupon: {
      override: boolean;
    };
    editor: {
      enabled: boolean;
    };
    product: {
      show_out_of_stock_price: boolean;
    };
    checkout: {
      force_user_auth: boolean;
    };
    gratuity: {
      type: number;
      label: string;
      value: number;
      pickup: boolean;
      enabled: boolean;
      delivery: boolean;
    };
    future_order: number;
  };
  contact_phone: string;
  email: string;
  contact_name: string;
  tax_before_discount: number;
  preparation_time: number;
  decimal_places: number;
  payment_options: GenericMap[];
  address_api: string;
  branches: Branch[];
}

export interface Branch {
  id: number;
  r_id: number;
  address: string;
  location: string;
  country: string;
  city: string;
  phoneno: string;
  lat: string;
  lng: string;
  status: string;
  email: string;
  delivery: number;
  pickup: number;
  reservation: number;
  delivery_service: number;
  time_zone: string;
  settings: string;
  delivery_settings: string;
  email_settings: string | null;
  sms_settings: string;
  timing: string;
}

export interface UserAuthResponse {
  user_id: string;
  user_email: string;
  user_fullname: string;
  user_gender: string;
  user_address: string;
  user_fbid: string;
  app_secret: string;
  user_dob: string;
  user_cphone: string;
  td_user_id: string;
  user_city: string;
  payment_settings?: { stripe?: string };
  token: string;
  refresh_token: string;
}

// ==============================|| abstracted API types ||============================== //

export interface GetUserDistanceRequest {
  userDetails: { userLocation: google.maps.LatLngLiteral; postalCode: string };
  businessDetails: { branchId: string; businessId: string };
}

export interface GetUserDistanceResponse {
  distance: number;
}

export interface GetPickupSlotsRequest {
  date: string;
  location?: string;
  bid?: string;
  type?: string;
}

export interface GetPickupSlotsResponse {
  slots: string[];
}

export interface CartAbstractedRequest {
  businessId: string;
  branchId: string;
  action: CartActions;
  orderCartId: string;
  orderType: string;
  product: ProductsList | CartItem;
  cartDetails: Cart;
}

export interface CartRequest {
  business_id: string;
  branch_id: string;
  action: CartActions;
  current_date: string;
  unique_order_id: string;
  order_type: string;
  items: CartRequestItem[];
}

export interface CartRequestItem {
  id: string;
  image: string;
  name: string;
  price: string;
  qty: number;

  category_id: string;
  category_name: string;
  brand_id: string;

  discount: number;
  item_level_discount_value: number;

  tax: string;
  item_level_tax_value: number;

  weight_value: string;
  weight_unit: string;

  comment: string;
  product_code: string;
  options: GenericMap;
}

export interface CartResponse {
  orderid: number;
  temp_order_id: string;
  total: number;
  tax: number;
  tax_value: number;
  discount: number;
  discount_value: number;
  gtotal: number;
  status: string;
  ordertype: string;
  delivery_charges: number;
  delivery_tax: number;
  delivery_tax_value: number;
  items: {
    odetailid: string;
    orderid: number;
    dname: string;
    dqty: number;
    dprice: number;
    dtotal: number;
    item_level_grand_total: number;
    comment: string;
    option_set: string;
    menu_item_id: string;
    discount: number;
    item_level_discount_value: number;
    coupon_discount: number;
    tax: number;
    item_level_tax_value: number;
    weight_value: number;
    weight_unit: string;
    category_id: string;
    brand_id: string;
    product_code: string;
    category_name: string;
    status: string;
  }[];
}

export interface Cart {
  subTotal: string;
  itemsCount: number;
  items: { [id: string]: CartItem };
}

export interface CartItem {
  id: string;
  name: string;
  price: string;
  quantity: number;
  currency: string;
  image: string;
  inventory: string;
  categoryId: string;
  category: string;
}
