import { Metadata } from "next";
import Image from "next/image";

import { FeaturedProducts, OrderForm, Testimonials } from "@/app/components";

import { PageSEODetails } from "@/types";
import { getPageSEOData } from "@/lib/apiConfigs";
import { createMetadata } from "@/lib/utils/helperFunctions";

// generate metadata for the home page (SSR)
export const generateMetadata = async (): Promise<Metadata> => {
  // function to call SEO API to fetch page SEO data
  const seoDetails: PageSEODetails = await getPageSEOData("home");

  // function call to create Next.js metadata from SEO data
  return createMetadata(seoDetails);
};

// home page component
const Home = async () => {
  // ==============================|| API calls ||============================== //

  // function to call SEO API to fetch page h1
  const seoDetails: PageSEODetails = await getPageSEOData("home");

  // ==============================|| UI ||============================== //

  return (
    <main>
      {/* hidden h1 for SEO */}
      <h1 className="sr-only">{seoDetails?.h1}</h1>

      {/* hero section */}
      <section className="relative w-full !border-t !border-black">
        <div className="w-full relative">
          <div className="h-full home_banner">
            <Image
              src="https://static.tossdown.com/images/0e43c47f-0da8-4c1e-92e4-8d71f2f9645d.webp"
              alt="Banner"
              width={1521}
              height={280}
              priority
              unoptimized
              layout="responsive"
              className="banner_desk_img"
            />

            <Image
              src="https://static.tossdown.com/site/7fe79ac2-7131-4f73-92fd-822facbde6dc.webp"
              alt="Banner"
              width={7008}
              height={8279}
              priority
              unoptimized
              layout="responsive"
              className="banner_mobile_img"
            />

            {/* order form */}
            <OrderForm />
          </div>
        </div>
      </section>

      {/* how it works section */}
      <section className="custom_2_parent">
        <div className="custom_2_header">
          <small>
            <strong></strong>
            <span></span>
            <span></span>
            <span></span>
          </small>

          <h3>HOW IT WORKS</h3>

          <small>
            <span></span>
            <span></span>
            <span></span>
            <strong></strong>
          </small>
        </div>

        <div className="custom_2_body">
          <div className="custom_container">
            <div className="custom_2_data">
              <div className="custom_2_single">
                <figure>
                  <Image
                    src="https://static.tossdown.com/images/010feec2-0544-4f83-9748-cb9c374bdc10.webp"
                    alt="Enter Address"
                    width={77}
                    height={77}
                    className="object-contain"
                  />
                </figure>
                <span>Enter Address</span>
              </div>

              <div className="custom_2_single">
                <figure>
                  <Image
                    src="https://static.tossdown.com/images/ac0c42a7-02cc-4165-af98-e44ee0cc8506.webp"
                    alt="Schedule Delivery Type"
                    width={77}
                    height={77}
                    className="object-contain"
                  />
                </figure>
                <span>Schedule Delivery Type</span>
              </div>

              <div className="custom_2_single">
                <figure>
                  <Image
                    src="https://static.tossdown.com/images/8bbc0bbe-dccf-42b4-890c-4a50c594eb0a.webp"
                    alt="Select Your Meals"
                    width={77}
                    height={77}
                    className="object-contain"
                  />
                </figure>
                <span>Select Your Meals</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* featured products */}
      <FeaturedProducts />

      {/* testimonials/reviews */}
      <Testimonials />

      {/* Sign Up Now button - only shown when user is not authenticated */}
      {/* {!isAuthenticated && (
        <div className="signup_btn mb-[60px]">
          <a
            href="#"
            id="signupClick"
            onClick={(e) => {
              e.preventDefault();
              // Show the signup modal
              setAuthModalView("signup");
              setAuthModalOpen(true);
            }}
          >
            Sign Up Now
          </a>
        </div>
      )} */}

      {/* Auth Modal */}
      {/* <AuthModalWrapper
        isOpen={authModalOpen}
        initialView={authModalView}
        onClose={() => setAuthModalOpen(false)}
      /> */}
    </main>
  );
};

export default Home;
