import React from "react";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faChevronDown } from "@fortawesome/free-solid-svg-icons";

import { SelectProps } from "../index.types";
import { DropDownList } from "@/types";

const DropDown: React.FC<SelectProps> = ({
  title,
  placeholder = "Select an option",
  value,
  options,
  icon,
  className = "",
  disabled = false,
  onChange,
}) => {
  return (
    <div className={`relative banner_form_field ${className}`}>
      {/* display icon if provided */}
      {icon && (
        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
          {icon}
        </div>
      )}

      <select
        id={title}
        title={title}
        value={value}
        className={`h-full w-full md:w-[377px] p-2 ${
          icon ? "pl-10" : "!pl-3"
        } appearance-none bg-white text-black font-poppins ${
          !disabled && "cursor-pointer"
        }`}
        disabled={disabled}
        onChange={onChange}
      >
        <option value="" disabled>
          {placeholder}
        </option>

        {options?.map((option: DropDownList) => (
          <option key={option?.value} value={option?.value}>
            {option?.label}
          </option>
        ))}
      </select>

      {/* dropdown icon */}
      {!disabled && (
        <div className="absolute inset-y-0 right-0 flex items-center pr-2">
          <FontAwesomeIcon icon={faChevronDown} size="xs" />
        </div>
      )}
    </div>
  );
};

export default DropDown;
