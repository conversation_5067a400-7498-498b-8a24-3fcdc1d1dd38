import { Metadata } from "next";

import "@/styles/seoPage.styles.css";
import { PageSEODetails } from "@/types";
import { createMetadata } from "@/lib/utils/helperFunctions";

// generate metadata for the SEO static page (SSR)
export const generateMetadata = async (): Promise<Metadata> => {
  // SEO details
  const seoDetails: PageSEODetails = {
    pagetitle:
      "Enjoy the Best Indian Food with Quick Delivery in Mississauga, Brampton and Toronto",
    h1: "Best Indian Food Delivery Service in Mississauga, Brampton & Toronto | EZeats",
    desc: "Looking for authentic Indian food? EZeats offers fast, affordable Indian food delivery in Mississauga, Brampton, and Toronto. Enjoy fresh vegetarian and non-vegetarian meals delivered to your door with our customizable meal subscriptions.",
    keywords: "Indian food, indian food near me, indian food delivery",
  };

  // function call to create Next.js metadata from SEO data
  return createMetadata(seoDetails);
};

// indian food delivery service page component
const IndianFoodDeliveryService: React.FC = () => {
  return (
    <main>
      {/* hidden h1 for SEO */}
      <h1 className="sr-only">
        Best Indian Food Delivery Service in Mississauga, Brampton & Toronto |
        EZeats
      </h1>

      <div className="page_top_padding"></div>

      <div className="static_breadcrums_parent">
        <div className="static_container">
          <div className="static_breadcrums">
            <a href="/">HOME</a>
            <span>/</span>
            <a href="/menu">Indian Food Delivery Service</a>
          </div>
        </div>
      </div>

      <div className="custom_sec_11_parent">
        <div className="static_container">
          <div className="custom_sec_11_data custom_sec_11_reverse">
            <div className="custom_sec_11_data_body">
              <div className="custom_sec_11_data_heading">
                <h2>
                  EZeats: Your Go-To for Fresh Indian Food Service and Fast
                  Delivery in the GTA
                </h2>
                <div className="custom_sec_11_des">
                  <p>
                    We’ve all been there: you’re starving, but cooking feels
                    like the last thing you want to do. That’s where{" "}
                    <a href="/">EZeats</a> comes in. Whether you’re in the mood
                    for a comforting vegetarian meal, craving quick{" "}
                    <strong>Indian food delivery</strong>, or hosting
                    last-minute guests, <a href="/">EZeats</a> makes it easy to
                    get authentic, mouthwatering <strong>Indian food</strong>{" "}
                    delivered straight to your door in{" "}
                    <strong>Mississauga, Brampton</strong>, and{" "}
                    <strong>Toronto.</strong>
                  </p>
                </div>
              </div>
              <div className="custom_sec_11_btn">
                <a href="/menu">Order Now</a>
              </div>
            </div>
            <figure>
              <img
                src="https://static.tossdown.com/images/d7c9f82b-9fb7-4de8-9088-030b75775710.webp"
                alt="EZeats: Your Go-To for Fresh Indian Food Service and Fast Delivery in the GTA"
              />
            </figure>
          </div>
        </div>
      </div>

      <section className="cus_sec3_parent">
        <div className="static_container">
          <div className="cus_sec3_box">
            <div className="cus_sec3_data">
              <h2>Fresh, Flavorful Vegetarian Indian Food</h2>
              <div className="cus_sec3_inner_data">
                <p>
                  At <a href="/">EZeats</a>, we believe great food starts with
                  great ingredients. Our vegetarian dishes are made with fresh
                  produce, authentic spices, and all the love you’d expect from
                  a homemade meal. From hearty dal and channay to creamy{" "}
                  <a href="/product/shahi-paneer-with-roti-306453">
                    paneer dishes
                  </a>
                  , every bite is packed with flavor. Here are a few of our
                  crowd favorites:
                </p>
              </div>
              <div className="cus_sec3_inner_data">
                <ul>
                  <li>
                    <a href="/product/chana-masala-with-roti-306436">
                      Chana Masala
                    </a>
                    – Spicy chickpeas that slap. Packed with rich flavors and
                    perfect for any meal.
                  </li>
                  <li>
                    <a href="/product/aloo-gobi-with-roti-306449">Aloo Gobi</a>{" "}
                    – Comfort food that never fails. The perfect combo of
                    potatoes and cauliflower, seasoned just right.
                  </li>
                  <li>
                    <a href="/product/palak-paneer-with-roti-306450">
                      Palak Paneer
                    </a>
                    – Creamy, dreamy greens. Spinach and paneer that’ll have you
                    going back for more.
                  </li>
                  <li>
                    <a href="/product/shahi-paneer-with-roti-306453">
                      Shahi Paneer
                    </a>
                    – For when you're feelin’ fancy. Rich, indulgent, and full
                    of flavor.
                  </li>
                  <li>
                    <a href="/product/rajma-chawal-306448">Rajma Chawal</a> – A
                    hug in a bowl. The ultimate comfort meal, with soft kidney
                    beans and basmati rice.
                  </li>
                  <li>
                    <a href="/product/bhindi-masala-with-roti-306445">
                      Bhindi Masala
                    </a>
                    – Delicious okra magic. Lightly spiced and crispy, it’s a
                    veggie dish you won’t want to miss.
                  </li>
                  <li>
                    <a href="/product/kadhi-chawal-306454">Kadhi Chawal</a> – A
                    comforting bowl of tangy kadhi paired perfectly with fluffy
                    rice.
                  </li>
                  <li>
                    <a href="/product/masoor-daal-with-rice-306444">
                      Masoor Daal with Rice
                    </a>
                    – Hearty, wholesome masoor dal served with soft, steaming
                    rice.
                  </li>
                  <li>
                    <a href="/product/aloo-sabzi-with-roti-306447">
                      Aloo Sabzi with Roti
                    </a>
                    – Classic potato sabzi with soft roti – simple, satisfying,
                    and soul-warming.
                  </li>
                  <li>
                    <a href="/product/vegetable-curry-with-rice-306437">
                      Vegetable Curry with Rice
                    </a>
                    – A medley of veggies in a rich, flavorful curry served with
                    premium Basmati rice.
                  </li>
                  <li>
                    <a href="/product/dal-makhni-with-roti-355361">
                      Dal Makhni with Roti
                    </a>
                    – Creamy, smoky dal makhni paired with soft, warm roti for
                    the ultimate comfort meal.
                  </li>
                  <li>
                    <a href="/product/pav-bhaji-306446">Pav Bhaji</a> – Spicy,
                    buttery vegetable bhaji served with soft pav – street food
                    at its best!
                  </li>
                </ul>
                <p>
                  With these dishes, we’ve got something for everyone—whether
                  you’re sticking to a vegetarian diet or just love great food.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div className="custom_sec_11_parent">
        <div className="static_container">
          <div className="custom_sec_11_data custom_sec_11_reverse">
            <div className="custom_sec_11_data_body">
              <div className="custom_sec_11_data_heading">
                <h2>Fast, Affordable Indian Food Delivery</h2>
                <div className="custom_sec_11_des">
                  <p className="mb-3">
                    Life’s busy, and sometimes you just want a delicious, hot
                    meal without the wait or the work. That’s exactly why we’ve
                    made it easy for you to get fresh
                    <a href="/menu"> Indian food </a> delivered right to your
                    doorstep. Whether you’re at home or the office, we’ll bring
                    your favorites straight to you, hot and ready to eat. And,
                    the best part? Our meals start at only $5.99!
                  </p>
                  <p>
                    We believe you should be able to enjoy{" "}
                    <strong>Indian food near you </strong> at a price that makes
                    sense. No hidden fees, just amazing food that’s affordable
                    for everyone.
                  </p>
                </div>
              </div>
              <div className="custom_sec_11_btn">
                <a href="/menu">Order Now</a>
              </div>
            </div>
            <figure>
              <img
                src="https://static.tossdown.com/images/dbd26b1f-ea8e-43e1-98a7-ea28ec00372d.webp"
                alt=" Fast, Affordable Indian Food Delivery"
              />
            </figure>
          </div>
        </div>
      </div>

      <section className="cus_sec3_parent">
        <div className="static_container">
          <div className="cus_sec3_box">
            <div className="cus_sec3_data">
              <h2>Meal Subscriptions for Busy Families and Professionals</h2>
              <div className="cus_sec3_inner_data">
                <p>
                  Between work, school, and life’s never-ending demands, who has
                  time to cook every day? That’s where our meal subscription
                  service comes in. Whether you’re a busy parent, a student, or
                  a professional with a packed schedule, we’ve got you covered.
                  With weekly/bi-weekly plans, we’ll deliver fresh, flavorful{" "}
                  <a href="/menu">Indian food</a> right to your door, no
                  shopping, no cooking, just delicious meals when you need them.
                </p>
                <p>
                  Our subscriptions fit your schedule and preferences, so you’ll
                  never have to worry about what’s for dinner again.
                </p>
              </div>
            </div>
            <div className="cus_sec3_data">
              <h2>Perfect Indian Food for Special Occasions</h2>
              <div className="cus_sec3_inner_data">
                <p>
                  Planning a dinner party or hosting friends for a special
                  occasion? Let us take the stress out of mealtime.{" "}
                  <a href="/">EZeats</a> has a wide range of vegetarian{" "}
                  <a href="/menu">Indian dishes </a> that are sure to impress
                  your guests. From birthdays to celebrations, our food is the
                  perfect way to make any event feel a little more special.
                </p>
              </div>
            </div>
            <div className="cus_sec3_data">
              <h2>Last-Minute Guests? No Problem!</h2>
              <div className="cus_sec3_inner_data">
                <p>
                  Got unexpected visitors? Don’t worry! Whether it’s a surprise
                  dinner party or a few friends stopping by, we’ll make sure you
                  have hot, delicious food on the table in no time. With our
                  quick <strong>Indian food delivery</strong>, you’ll always be
                  ready to entertain, no matter how last minute it is.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="cus_sec4_parent">
        <div className="static_container">
          <div className="cus_sec4_box">
            <div className="cus_sec4_data">
              <h4>Delivering in GTA</h4>
            </div>
          </div>
        </div>
      </section>

      <section className="cus_sec3_parent">
        <div className="static_container">
          <div className="cus_sec3_box">
            <div className="cus_sec3_data">
              <div className="faq_sec">
                <div className="single_faq">
                  <h4>How fast is the delivery?</h4>
                  <p>
                    We aim to deliver most orders within an hour, so your food
                    is always fresh and hot when it arrives.
                  </p>
                </div>
                <div className="single_faq">
                  <h4>Do you offer vegetarian options?</h4>
                  <p>
                    Absolutely! Our menu is packed with a variety of vegetarian{" "}
                    <a href="/menu">Indian dishes</a>. Whether you’re vegetarian
                    or just looking for a plant-based meal, we’ve got you
                    covered.
                  </p>
                </div>
                <div className="single_faq">
                  <h4>Are your meals affordable?</h4>
                  <p>
                    Yes! We believe in offering high-quality{" "}
                    <a href="/menu">Indian food</a> at prices that are easy on
                    your wallet. You don’t have to spend a lot to enjoy great
                    meals.
                  </p>
                </div>
                <div className="single_faq">
                  <h4>How does the meal subscription work?</h4>
                  <p>
                    Simple! You can choose a weekly or bi-weekly plan, and we’ll
                    deliver fresh, flavorful <a href="/menu">Indian meals</a>{" "}
                    right to your door. It’s that easy.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="cus_sec3_parent">
        <div className="static_container">
          <div className="cus_sec3_box">
            <div className="cus_sec3_data">
              <h2>Life Made EZ!</h2>
              <div className="cus_sec3_inner_data">
                <p>
                  Whether you’re looking for a quick bite, a meal plan that fits
                  your lifestyle, or catering for a special event,{" "}
                  <a href="/">EZeats</a> is here to make your life easier and a
                  lot tastier. With fast delivery, delicious meals, and prices
                  that make sense, we’re your go-to for all things Indian food
                  in
                  <strong> Mississauga, Brampton,</strong> and{" "}
                  <strong>Toronto</strong>.
                </p>
                <p>
                  <a href="/menu"> Order now</a>, and let us bring the authentic
                  taste of India to your doorstep with our fast and reliable{" "}
                  <strong>Indian food delivery!</strong>
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
};

export default IndianFoodDeliveryService;
