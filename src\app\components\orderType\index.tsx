"use client";

import React from "react";

import { useSelector, dispatch } from "@/lib/store";
import { updateOrderStore } from "@/lib/store/slices/order";

const OrderType: React.FC = () => {
  const { orderTypes, orderType } = useSelector((state) => state.order);

  // ==============================|| handler functions ||============================== //

  const handleOrderTypeChange = (orderType: string): void => {
    // update store
    dispatch(updateOrderStore({ type: "orderType", value: orderType }));
  };

  // ==============================|| UI ||============================== //

  return (
    <div
      className={`grid grid-cols-${orderTypes?.length} w-full max-w-[140px] rounded-full bg-gray-100 md:bg-white md:mb-4`}
    >
      {orderTypes?.map((type) => (
        <button
          key={type}
          className={`text-[11px] md:text-[13px] font-semibold leading-[29px] text-center px-2 md:p-0 capitalize rounded-full cursor-pointer ${
            orderType === type && "bg-black text-white"
          }`}
          onClick={() => handleOrderTypeChange(type)}
          type="button"
        >
          {type}
        </button>
      ))}
    </div>
  );
};

export default OrderType;
