"use client";

import React from "react";

import { Button } from "@/app/components";
import { TabsProps } from "../index.types";

const Tabs: React.FC<TabsProps> = ({
  options,
  value,
  size,
  className = "",
  tabClassName = "",
  onClick,
}) => {
  return (
    <div className={`bg-gray-200 rounded-full flex w-full ${className}`}>
      {options?.map((option: number | string) => (
        <Button
          key={option}
          variant="primary"
          size={size}
          className={`flex-1 rounded-full font-bold text-xl transition-colors ${
            value === option
              ? "!bg-black text-white"
              : "!bg-transparent !text-black"
          } ${tabClassName}`}
          onClick={() => onClick?.(option)}
        >
          {option}
        </Button>
      ))}
    </div>
  );
};

export default Tabs;
