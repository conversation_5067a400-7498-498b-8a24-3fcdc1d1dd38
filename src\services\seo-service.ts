import { XMLParser } from "fast-xml-parser";

export interface SeoData {
  pagetitle?: string;
  h1?: string;
  keywords?: string;
  desc?: string;
}

export interface PageSeo {
  seo: SeoData;
  generated: SeoData;
}

// Cache the SEO data to avoid fetching it multiple times
let cachedSeoData: Record<string, PageSeo> | null = null;
let lastFetchTime = 0;
const CACHE_DURATION = 3600000; // 1 hour in milliseconds

/**
 * Fetches and parses the SEO XML data
 */
export async function fetchSeoData(): Promise<Record<string, PageSeo>> {
  const now = Date.now();

  // Return cached data if available and not expired
  if (cachedSeoData && now - lastFetchTime < CACHE_DURATION) {
    return cachedSeoData;
  }

  try {
    // Fetch the XML data from the endpoint
    const response = await fetch(
      "https://tossdown-images-live.s3.amazonaws.com/seo/458.xml",
      {
        cache: "no-store", // Always fetch fresh data
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch SEO data: ${response.status}`);
    }

    const xmlText = await response.text();

    // Parse the XML data
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: "",
      isArray: (name) => name === "page",
      parseAttributeValue: true,
      trimValues: true,
    });

    const result = parser.parse(xmlText);

    // Transform the data into a map for easier access
    const seoMap: Record<string, PageSeo> = {};

    if (result.results && Array.isArray(result.results.page)) {
      result.results.page.forEach((page: any) => {
        // Use the page name attribute as the key
        const pageName = page.name;

        // Only include fields that exist in the XML and have content
        const seoData: SeoData = {};

        // Check if the field exists and is not empty
        if (page.seo.pagetitle && page.seo.pagetitle.trim() !== "") {
          seoData.pagetitle = page.seo.pagetitle;
        }

        if (page.seo.h1 && page.seo.h1.trim() !== "") {
          seoData.h1 = page.seo.h1;
        }

        // For keywords and desc, check if they exist and have content
        // XML empty tags might be parsed as empty strings or null
        if (
          page.seo.keywords &&
          typeof page.seo.keywords === "string" &&
          page.seo.keywords.trim() !== ""
        ) {
          seoData.keywords = page.seo.keywords;
        }

        if (
          page.seo.desc &&
          typeof page.seo.desc === "string" &&
          page.seo.desc.trim() !== ""
        ) {
          seoData.desc = page.seo.desc;
        }

        // Do the same for generated data
        const generatedData: SeoData = {};

        if (
          page.generated.pagetitle &&
          page.generated.pagetitle.trim() !== ""
        ) {
          generatedData.pagetitle = page.generated.pagetitle;
        }

        if (page.generated.h1 && page.generated.h1.trim() !== "") {
          generatedData.h1 = page.generated.h1;
        }

        if (
          page.generated.keywords &&
          typeof page.generated.keywords === "string" &&
          page.generated.keywords.trim() !== ""
        ) {
          generatedData.keywords = page.generated.keywords;
        }

        if (
          page.generated.desc &&
          typeof page.generated.desc === "string" &&
          page.generated.desc.trim() !== ""
        ) {
          generatedData.desc = page.generated.desc;
        }

        seoMap[pageName] = {
          seo: seoData,
          generated: generatedData,
        };
      });
    }

    // Cache the data and update last fetch time
    cachedSeoData = seoMap;
    lastFetchTime = now;

    return seoMap;
  } catch (error) {
    console.error("Error fetching or parsing SEO data:", error);
    // Return empty object on error
    return {};
  }
}

/**
 * Gets SEO data for a specific page
 */
export async function getPageSeo(pageName: string): Promise<PageSeo | null> {
  const seoData = await fetchSeoData();

  if (!seoData[pageName]) {
    console.warn(`No SEO data found for page: ${pageName}`);
    return null;
  }

  return seoData[pageName];
}

/**
 * Gets SEO data for a product detail page, replacing placeholders
 */
export async function getProductSeo(
  productName: string,
  productDescription: string = ""
): Promise<SeoData | null> {
  const seoData = await fetchSeoData();
  const detailPage = seoData["detail"];

  if (!detailPage) {
    console.warn("No SEO data found for product detail page");
    return null;
  }

  const replacePlaceholders = (val?: string) =>
    typeof val === "string"
      ? val
          .replace(/<product_name>/g, productName)
          .replace(/<product_desc>/g, productDescription)
      : undefined;

  const srcSeo = detailPage.seo || {};
  const srcGen = detailPage.generated || {};

  const productSeo: SeoData = {};

  // pagetitle
  productSeo.pagetitle =
    replacePlaceholders(srcSeo.pagetitle) ||
    replacePlaceholders(srcGen.pagetitle);

  // h1
  productSeo.h1 =
    replacePlaceholders(srcSeo.h1) || replacePlaceholders(srcGen.h1);

  // desc
  productSeo.desc =
    replacePlaceholders(srcSeo.desc) || replacePlaceholders(srcGen.desc);

  // keywords (also replace placeholders if present)
  productSeo.keywords =
    replacePlaceholders(srcSeo.keywords) ||
    replacePlaceholders(srcGen.keywords);

  return productSeo;
}
