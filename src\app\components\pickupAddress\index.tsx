import React from "react";

import { LocationPin } from "../icons";

import { Select } from "@/app/components";
import { FieldError } from "@/app/components";

import { PickupAddressProps } from "../index.types";
import { Locations, OrderTypes } from "@/types";
import { getFilteredLocationsList } from "@/lib/utils/helperFunctions";
import { useSelector } from "@/lib/store";

const PickupAddress: React.FC<PickupAddressProps> = ({
  selectedLocation,
  setSelectedLocation,
}) => {
  const { locations } = useSelector((state) => state.business);

  // ==============================|| handler functions ||============================== //

  // function to handle location change
  const handleLocationChange = (
    event: React.ChangeEvent<HTMLSelectElement>
  ): void => {
    // find the selected branch
    const location: Locations | undefined = locations?.find(
      (location: Locations) => location?.id === event?.target?.value
    );

    // update selected branch if found
    if (location) {
      setSelectedLocation({ location, error: "" });
    }
  };

  // ==============================|| UI ||============================== //

  return (
    <div>
      <Select
        title="Pickup address"
        placeholder="Select pickup location"
        value={selectedLocation?.location?.id || ""}
        options={getFilteredLocationsList(locations, OrderTypes.PICKUP)}
        icon={<LocationPin size={20} />}
        className="h-[61px] max-[540px]:h-[50px]"
        onChange={handleLocationChange}
      />

      <FieldError errorMessage={selectedLocation?.error} />
    </div>
  );
};

export default PickupAddress;
