import { NextRequest, NextResponse } from "next/server";

import axios from "@/lib/axios";

import { GetUserDistanceRequest } from "@/types";
import { getUserDistance, getUserDistanceAPIPayload } from "@/lib/apiConfigs";

// get user distance API route handler
export async function POST(request: NextRequest) {
  try {
    // parse request data
    const requestData: GetUserDistanceRequest = await request?.json();

    // validate required fields
    if (
      !requestData?.userDetails?.userLocation?.lat ||
      !requestData?.userDetails?.userLocation?.lng ||
      !requestData?.businessDetails?.branchId ||
      !requestData?.businessDetails?.businessId
    ) {
      // return error if data not found
      return NextResponse.json(
        { data: null, meta: null, error: "Incomplete details" },
        { status: 400 }
      );
    }

    // get user distance API call
    const { data } = await axios({
      ...getUserDistance(),
      data: getUserDistanceAPIPayload(requestData),
    });

    // verify API response
    if (data?.user_distance) {
      // return success response
      return NextResponse.json(
        { data: { distance: data?.user_distance }, meta: null, error: null },
        { status: 200 }
      );
    }

    // return error if user distance not found
    return NextResponse.json(
      { data: null, meta: null, error: null },
      { status: 404 }
    );
  } catch (error) {
    // return error response
    return NextResponse.json(
      { data: null, meta: null, error },
      { status: 500 }
    );
  }
}
