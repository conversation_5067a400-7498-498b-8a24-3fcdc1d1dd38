"use client";

import { useState } from "react";
import Image from "next/image";

import { Button, Tabs } from "@/app/components";

import { businessName, headerLogo, mealBoxSize } from "@/lib/utils/constants";
import { dispatch, useSelector } from "@/lib/store";
import { updateOrderStore } from "@/lib/store/slices/order";

const MealBox = () => {
  const { boxSize } = useSelector((state) => state.order);

  const [mealBox, setMealBox] = useState<number>(boxSize || mealBoxSize.SMALL);

  // ==============================|| handler functions ||============================== //

  // function to handle box size change
  const handleBoxSizeChange = (value: number | string): void => {
    // update local state
    setMealBox(Number(value) || mealBox);
  };

  // function to save box size to persisted store
  const handleSaveBoxSize = (): void => {
    // update store
    dispatch(updateOrderStore({ type: "boxSize", value: mealBox }));
  };

  // ==============================|| UI ||============================== //

  // hide if box size is already selected
  if (boxSize) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/50 z-10 flex items-start justify-center pt-11 px-8">
      <div className="bg-white w-full max-w-sm overflow-hidden">
        <div className="bg-yellow-400 p-4 flex text-center justify-center">
          <Image
            src={headerLogo}
            alt={businessName}
            width={100}
            height={35}
            priority
            className="object-contain w-[84px] h-[29px] md:w-[100px] md:h-[35px]"
          />
        </div>

        <div className="p-4 text-center">
          <div className="text-2xl font-normal text-center font-heading uppercase mb-5">
            Choose how many meals you want to order
          </div>

          <Tabs
            options={Object.values(mealBoxSize)}
            value={mealBox}
            size="large"
            className="mb-5"
            onClick={handleBoxSizeChange}
          />

          <Button
            variant="primary"
            size="large"
            className="!font-bold w-full rounded-full"
            onClick={handleSaveBoxSize}
          >
            CONTINUE
          </Button>
        </div>
      </div>
    </div>
  );
};

export default MealBox;
