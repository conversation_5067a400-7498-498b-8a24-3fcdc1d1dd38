import { NextRequest, NextResponse } from "next/server";

import axios from "@/lib/axios";

import { getPickupSlots } from "@/lib/apiConfigs";

// get pickup slots API route handler
export async function GET(request: NextRequest) {
  try {
    // extract query parameters from the request URL
    const searchParams: URLSearchParams = request?.nextUrl?.searchParams;

    const location: string = searchParams?.get("location") || "";
    const date: string = searchParams?.get("date") || "";

    // validate required parameters
    if (!location || !date) {
      // return error if data not found
      return NextResponse.json(
        { data: null, meta: null, error: "Incomplete details" },
        { status: 400 }
      );
    }

    // get pickup slots API call
    const { data } = await axios(
      getPickupSlots({ date, bid: location, type: "pickup" })
    );

    // verify API response
    if (data?.status === "200" && Array.isArray(data?.slots)) {
      // return success response
      return NextResponse.json(
        { data: { slots: data?.slots }, meta: null, error: null },
        { status: 200 }
      );
    }

    // return error if no slots found
    return NextResponse.json(
      { data: null, meta: null, error: null },
      { status: 404 }
    );
  } catch (error) {
    // return error response
    return NextResponse.json(
      { data: null, meta: null, error },
      { status: 500 }
    );
  }
}
