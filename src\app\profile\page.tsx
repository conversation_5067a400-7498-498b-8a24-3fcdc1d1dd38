import { Metadata } from "next";

import ClientView from "./clientView";
import { PageSEODetails } from "@/types";
import { createMetadata } from "@/lib/utils/helperFunctions";

// generate metadata for the profile page (SSR)
export const generateMetadata = async (): Promise<Metadata> => {
  // profile SEO details
  const profileSEODetails: PageSEODetails = {
    pagetitle: "Profile - EZeats Fresh Meal Delivery and Tiffin Service",
    h1: "Manage Your Profile - Fresh Meals Delivered",
    desc: "Manage your profile, view orders, subscriptions, and account settings. Your personal dashboard for fresh meal delivery service in GTA.",
    keywords:
      "profile, account, orders, subscriptions, meal delivery, food delivery, EZeats, GTA delivery",
  };

  // function call to create Next.js metadata from SEO data
  return createMetadata(profileSEODetails);
};

// profile page component
const Profile: React.FC = () => {
  return (
    <main>
      {/* hidden h1 for SEO */}
      <h1 className="sr-only">Manage Your Profile - Fresh Meals Delivered</h1>

      {/* client side UI */}
      <ClientView />
    </main>
  );
};

export default Profile;
