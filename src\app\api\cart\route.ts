import { NextRequest, NextResponse } from "next/server";

import axios from "@/lib/axios";

import { Cart, CartAbstractedRequest } from "@/types";
import { cart, cartAPIPayload, handleCartResponse } from "@/lib/apiConfigs";

// cart operations API route handler
export async function POST(request: NextRequest) {
  try {
    // parse abstracted request data
    const requestData: CartAbstractedRequest = await request?.json();

    if (
      !requestData?.action ||
      !requestData?.product?.id ||
      !requestData?.orderCartId
    ) {
      // return error if data not found
      return NextResponse.json(
        { data: null, meta: null, error: "Incomplete details" },
        { status: 400 }
      );
    }

    // cart API call
    const { data } = await axios({
      ...cart(),
      data: cartAPIPayload(requestData),
    });

    // verify API response
    if (data?.status === 200 && data?.result?.items) {
      /**
       * function to handle the cart API response
       * pass products & cartDetails to save product details missing in API response
       */
      const cartDetails: Cart = handleCartResponse(
        data?.result,
        requestData?.product,
        requestData?.cartDetails
      );

      // return success response
      return NextResponse.json(
        { data: cartDetails, meta: null, error: null },
        { status: 200 }
      );
    }

    // return error if cart not found
    return NextResponse.json(
      { data: null, meta: null, error: null },
      { status: 404 }
    );
  } catch (error) {
    // return error response
    return NextResponse.json(
      { data: null, meta: null, error },
      { status: 500 }
    );
  }
}
