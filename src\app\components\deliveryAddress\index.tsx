"use client";

import { useEffect, useState } from "react";
import { useLoadScript } from "@react-google-maps/api";

import { LocationPin } from "../icons";
import { FieldError } from "@/app/components";
import {
  AddressDetails,
  Locations,
  DeliveryCharges,
  ChargesTypes,
  DeliveryRanges,
  GetUserDistanceResponse,
} from "@/types";
import { DeliveryAddressProps } from "../index.types";
import { callUserDistanceAbstractedAPI } from "@/lib/apiConfigs";
import { findNearestBranch } from "@/lib/utils/helperFunctions";
import { useSelector } from "@/lib/store";

const DeliveryAddress = ({
  deliveryAddressState,
  setDeliveryAddressState,
  setSelectedLocation,
}: DeliveryAddressProps) => {
  const [addressInput, setAddressInput] = useState<string>("");

  const { locations } = useSelector((state) => state.business);

  // load google maps script
  const { isLoaded } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_APP_KEY || "",
    libraries: ["places"],
  });

  // ==============================|| useEffect hook ||============================== //

  // useEffect to initialize google maps autocomplete
  useEffect(() => {
    // function to initialize google maps autocomplete
    const autocomplete: google.maps.places.Autocomplete | undefined =
      initializeMapAutocomplete();

    // clean up map instance on unmount
    return () => {
      if (autocomplete) {
        google.maps.event.clearInstanceListeners(autocomplete);
      }
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoaded]);

  // useEffect to sync input value with stored address
  useEffect(() => {
    // verify if address exists then update local state
    if (deliveryAddressState?.data?.address) {
      setAddressInput(deliveryAddressState.data.address);
    }
  }, [deliveryAddressState?.data?.address]);

  // ==============================|| handler functions ||============================== //

  // function to initialize google maps autocomplete
  const initializeMapAutocomplete = ():
    | google.maps.places.Autocomplete
    | undefined => {
    // return if maps is not loaded
    if (!isLoaded) {
      return;
    }

    // get input element
    const inputElement: HTMLInputElement | null = document?.getElementById(
      "deliveryAddress"
    ) as HTMLInputElement | null;

    // return if input element is not found
    if (!inputElement) {
      return;
    }

    // create google places autocomplete instance
    const autocomplete: google.maps.places.Autocomplete =
      new google.maps.places.Autocomplete(inputElement, {
        componentRestrictions: { country: "ca" },
        types: ["address"],
      });

    // add listener for place change event
    autocomplete?.addListener("place_changed", () =>
      handlePlaceChange(autocomplete)
    );

    return autocomplete;
  };

  // handle place change event
  const handlePlaceChange = async (
    autocomplete: google.maps.places.Autocomplete
  ): Promise<void> => {
    // get place details
    const places: google.maps.places.PlaceResult = autocomplete?.getPlace();

    // verify if place has geometry
    if (!places?.geometry?.location) {
      return;
    }

    // function to parse place details
    parsePlaceDetails(places);
  };

  // function to parse place details
  const parsePlaceDetails = (place: google.maps.places.PlaceResult): void => {
    // initialize address details
    const addressDetails: AddressDetails = {
      address: place?.formatted_address || "",
      street: "",
      area: "",
      city: "",
      country: "",
      postalCode: "",
      location: place.geometry?.location?.toJSON() || null,
    };

    // update React state with the selected address
    setAddressInput(addressDetails.address);

    // verify if address components exist
    if (place?.address_components?.length) {
      // loop through address component to extract details
      for (const component of place?.address_components) {
        // extract street
        if (
          component?.types?.includes("premise") ||
          component?.types?.includes("street_number")
        ) {
          addressDetails.street = component?.long_name || "";
        }

        // extract city
        if (
          component?.types?.includes("locality") ||
          component?.types?.includes("administrative_area_level_3")
        ) {
          addressDetails.city = component?.long_name || "";
        }

        // extract area
        if (component?.types?.includes("route")) {
          addressDetails.area = component?.short_name || "";
        }

        // extract country
        if (component?.types?.includes("country")) {
          addressDetails.country = component?.long_name || "";
        }

        // extract postal code
        if (component?.types?.includes("postal_code")) {
          addressDetails.postalCode = component?.long_name || "";
        }
      }
    }

    // function to validate user address
    validateUserAddress(addressDetails);
  };

  // function to find nearest branch and call validate user address API
  const validateUserAddress = async (userAddress: AddressDetails) => {
    // variable to store if address is valid
    let isValidAddress: boolean = false;

    // function to find nearest branch according to user location
    const nearestBranch: Locations | null = findNearestBranch(
      userAddress?.location,
      locations
    );

    // validate user address if nearest branch is found and location is available
    if (nearestBranch && userAddress?.location) {
      // set loading state to true before making API call
      setDeliveryAddressState({
        ...deliveryAddressState,
        loading: true,
        error: "",
      });

      // get user distance abstracted API call
      const { data } = await callUserDistanceAbstractedAPI({
        userDetails: {
          userLocation: userAddress?.location,
          postalCode: userAddress.postalCode,
        },
        businessDetails: {
          branchId: nearestBranch?.id,
          businessId: process.env.NEXT_PUBLIC_BUSINESS_ID || "",
        },
      });

      // verify if data exists and branch has delivery settings
      if (data && nearestBranch?.deliverySettings) {
        // parse delivery settings from nearest branch
        const deliverySettings: DeliveryCharges | null =
          JSON.parse(nearestBranch?.deliverySettings) || null;

        // check if delivery is available by geo range
        if (deliverySettings?.charges_type === ChargesTypes.GEO_RANGE) {
          // check if user distance falls within any of the delivery radius ranges
          isValidAddress = deliverySettings?.charges_details?.charges?.some(
            (range: DeliveryRanges) => {
              return (
                (data as GetUserDistanceResponse)?.distance >=
                  parseFloat(range?.min_distance) &&
                (data as GetUserDistanceResponse)?.distance <=
                  parseFloat(range?.max_distance)
              );
            }
          );
        }
      }
    }

    // update delivery address state
    setDeliveryAddressState({
      data: userAddress,
      loading: false,
      error: isValidAddress ? "" : "Your area is not in our delivery radius",
    });

    // set delivery branch
    setSelectedLocation({
      location: isValidAddress ? nearestBranch : null,
      error: "",
    });
  };

  // ==============================|| UI ||============================== //

  return (
    <div className="relative banner_form_field">
      <div className="relative">
        <LocationPin
          className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500"
          size={20}
        />

        {/* address input field */}
        <input
          id="deliveryAddress"
          type="text"
          placeholder="Enter delivery address"
          value={addressInput}
          onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
            setAddressInput(event?.target?.value || "")
          }
          className={`w-full md:w-[377px] h-[62px] p-2 pl-10 text-black`}
        />

        {/* loading indicator in address field */}
        {deliveryAddressState?.loading && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            <div className="bg-white px-3 py-2 -ml-2 rounded-l-md">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900"></div>
            </div>
          </div>
        )}
      </div>

      {/* error message below address field */}
      <FieldError errorMessage={deliveryAddressState?.error} />
    </div>
  );
};

export default DeliveryAddress;
