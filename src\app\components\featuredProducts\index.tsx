"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";

import { Button, Menu, Skeleton } from "@/app/components";

import { ProductsList, RequestStateData } from "@/types";
import { callProductsAbstractedAPI } from "@/lib/apiConfigs";

const FeaturedProducts = () => {
  const [productsState, setProductsState] = useState<
    RequestStateData<ProductsList[]>
  >({
    data: [],
    loading: true,
    error: "",
  });

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function to call get products API
    callGetProductsAPI();
  }, []);

  // ==============================|| handler functions ||============================== //

  // function to call get products API
  const callGetProductsAPI = async (): Promise<void> => {
    // reset request state to loading
    setProductsState({ data: [], loading: true, error: "" });

    // get products abstracted API call
    const { data, error } = await callProductsAbstractedAPI();

    /**
     * if no error => set the products
     * else => set the error
     */
    if (!error) {
      // update the products state with data
      setProductsState({
        data:
          (data as ProductsList[])?.filter(
            (item: ProductsList) => item?.featured
          ) || [],
        loading: false,
        error: "",
      });
    } else {
      // update the request state with error
      setProductsState({
        data: [],
        loading: false,
        error: "Failed to load products. Please try again later.",
      });
    }
  };

  // ==============================|| UI ||============================== //

  // show skeleton loader while loading
  if (productsState?.loading) {
    return <Skeleton />;
  }

  // show error message if there's an error
  if (productsState?.error) {
    return (
      <section className="py-8">
        <div className="static_container mx-auto px-4">
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-4">Featured Products</h2>
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
              <p className="text-red-600 mb-4">{productsState?.error}</p>

              <Button onClick={callGetProductsAPI} variant="danger">
                Retry
              </Button>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    productsState?.data?.length > 0 && (
      <div>
        {/* section heading */}
        <section className="custom_3_parent">
          <div className="custom_3_header">
            <small>
              <strong></strong> <span></span>
              <span></span>
              <span></span>
            </small>

            <h3>Menu</h3>

            <small>
              <span></span>
              <span></span>
              <span></span>
              <strong></strong>
            </small>
          </div>
        </section>

        {/* display products */}
        <Menu products={productsState?.data} enableCart={false} />

        <div className="static_container !mb-8">
          <div className="flex justify-center w-full mt-12">
            <Link href="/our-menu">
              <Button
                variant="primary"
                size="large"
                className="text-[14px] font-normal leading-[23px] w-[132px] h-[40px] sm:w-[148px] sm:h-[43px] md:text-[16px] md:w-[155px] md:h-[42px] lg:text-[23px] lg:w-[208px] lg:h-[52px]"
              >
                View Full Menu
              </Button>
            </Link>
          </div>
        </div>
      </div>
    )
  );
};

export default FeaturedProducts;
