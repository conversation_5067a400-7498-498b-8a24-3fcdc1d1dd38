"use client";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import Image from "next/image";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faBars, faXmark } from "@fortawesome/free-solid-svg-icons";

import { ShoppingCart } from "../../icons";

import { Button } from "@/app/components";
import { NavigationMenu } from "@/app/components";
import { OrderType } from "@/app/components";
import { Auth } from "@/app/components";

import { BusinessDetails } from "@/types";
import { callBusinessDetailsAbstractedAPI } from "@/lib/apiConfigs";
import { businessName, cookiesKey, headerLogo } from "@/lib/utils/constants";
import { getCookies, processOrderTypes } from "@/lib/utils/helperFunctions";
import { dispatch, useSelector } from "@/lib/store";
import {
  bulkUpdateOrderStore,
  toggleCartDrawer,
  updateOrderStore,
} from "@/lib/store/slices/order";
import { bulkUpdateBusinessStore } from "@/lib/store/slices/business";

const ClientView: React.FC = () => {
  const [mobileMenu, setMobileMenu] = useState<boolean>(false);

  const { orderType, cartDetails } = useSelector((state) => state?.order);

  const pathname = usePathname();

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function to call business details API
    callBusinessDetailsAPI();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    // update user detail in store if not profile or checkout page
    if (
      !(pathname?.startsWith("/profile") || pathname?.startsWith("/checkout"))
    ) {
      dispatch(
        updateOrderStore({
          type: "userDetails",
          value: getCookies(cookiesKey?.userDetails),
        })
      );
    }
  }, [pathname]);

  // ==============================|| handler functions ||============================== //

  // function to call business details API
  const callBusinessDetailsAPI = async (): Promise<void> => {
    // business details abstracted API call
    const { data } = await callBusinessDetailsAbstractedAPI();

    // verify if data exists
    if (data) {
      // update business store
      dispatch(bulkUpdateBusinessStore(data));

      // function to process locations and set order types
      const { orderTypes, orderType: initialOrderType } = processOrderTypes(
        (data as BusinessDetails)?.locations
      );

      // update order store
      dispatch(
        bulkUpdateOrderStore({
          orderTypes,
          orderType: orderType || initialOrderType,
        })
      );
    }
  };

  // function to handle mobile menu
  const toggleMobileMenu = (): void => {
    setMobileMenu((prevState) => !prevState);
  };

  // ==============================|| UI ||============================== //

  if (pathname?.startsWith("/profile")) {
    return null;
  }

  // display checkout UI for header
  if (pathname?.startsWith("/checkout")) {
    return (
      <span className="text-lg">
        Checkout ({cartDetails?.itemsCount || 0}{" "}
        {cartDetails?.itemsCount === 1 ? "Item" : "Items"})
      </span>
    );
  }

  return (
    <>
      {/* Order types are now available in Redux store: {orderTypes.join(', ')} */}
      <div className="flex items-center space-x-3">
        {/* order type component for small screen - only visible on home page */}
        {pathname === "/" && (
          <div className="block md:hidden">
            <OrderType />
          </div>
        )}

        {/* auth/profile */}
        <Auth />

        <Button
          title="Cart"
          variant="ghost"
          className="relative cursor-pointer !p-0"
          onClick={() => dispatch(toggleCartDrawer())}
        >
          <ShoppingCart className="w-5 h-5 md:w-6 md:h-6" />

          <span className="absolute -top-2 -right-2 bg-[#ffa800] text-white text-xs font-semibold text-center leading-[18px] rounded-full w-[18px] h-[18px] pl-[1px]">
            {cartDetails?.itemsCount || 0}
          </span>
        </Button>

        {/* hamburger icon for mobile view */}
        <div className="flex md:hidden">
          <Button
            title="Toggle menu"
            variant="ghost"
            className="cursor-pointer !p-0"
            onClick={toggleMobileMenu}
          >
            <FontAwesomeIcon icon={faBars} size="lg" />
          </Button>
        </div>
      </div>

      {/* navigation menu drawer in mobile */}
      {mobileMenu && (
        <div
          className="fixed inset-0 bg-black/50 z-10 flex md:hidden"
          onClick={toggleMobileMenu}
        >
          <div
            className="p-6 bg-white w-4/5 max-w-xs h-full overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-5">
              {/* business logo */}
              <Link
                href="/"
                className="flex items-center"
                onClick={toggleMobileMenu}
              >
                <Image
                  src={headerLogo}
                  alt={businessName}
                  width={100}
                  height={40}
                  className="object-contain"
                />
              </Link>

              <Button
                variant="ghost"
                aria-label="Close menu"
                className="cursor-pointer !p-0"
                onClick={toggleMobileMenu}
              >
                <FontAwesomeIcon icon={faXmark} />
              </Button>
            </div>

            {/* mobile navigation */}
            <nav>
              <div className="flex flex-col md:hidden">
                <NavigationMenu
                  className="text-sm font-normal border-b border-gray-300 py-4"
                  onNavigate={toggleMobileMenu}
                  displayIcon
                />
              </div>
            </nav>
          </div>
        </div>
      )}
    </>
  );
};

export default ClientView;
