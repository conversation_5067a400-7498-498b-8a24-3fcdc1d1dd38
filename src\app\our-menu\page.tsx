import { Metadata } from "next";

import { <PERSON>u, OrderNow } from "@/app/components";

import { PageSEODetails } from "@/types";
import { getPageSEOData, callProductsAPI } from "@/lib/apiConfigs";
import { createMetadata } from "@/lib/utils/helperFunctions";

// generate metadata for the menu page (SSR)
export const generateMetadata = async (): Promise<Metadata> => {
  // function to call SEO API to fetch page SEO data
  const seoDetails: PageSEODetails = await getPageSEOData("menu");

  // function call to create Next.js metadata from SEO data
  return createMetadata(seoDetails);
};

// our menu page component
const OurMenu = async () => {
  // ==============================|| API calls ||============================== //

  // function to call SEO API to fetch page h1
  const seoDetails: PageSEODetails = await getPageSEOData("menu");

  // function to call products API
  const { data, error } = await callProductsAPI();

  // ==============================|| UI ||============================== //

  return (
    <main>
      <section className="static_container mx-auto py-8 md:py-12">
        {/* hidden h1 for SEO */}
        <h1 className="sr-only">{seoDetails?.h1}</h1>

        {/* page heading */}
        <div className="flex items-center justify-center w-full pb-8">
          <h2 className="font-heading text-[46px] leading-[50px] font-normal text-left max-[992px]:text-[36px] max-[992px]:leading-[42px]">
            OUR MENU
          </h2>
        </div>

        {/* error message */}
        {!!error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto mb-8">
            <p className="text-red-600 text-center">
              {
                "Unable to load menu items at the moment. Please try refreshing the page."
              }
            </p>
          </div>
        )}

        {/* menu */}
        {!error && <Menu products={data} enableCart={false} />}
      </section>

      {/* order now button */}
      <OrderNow />
    </main>
  );
};

export default OurMenu;
