"use client";

import React from "react";
import { useRouter } from "next/navigation";

import { Button } from "@/app/components";
import { useSelector } from "@/lib/store";
import {
  userNotification,
  validateOrderForm,
} from "@/lib/utils/helperFunctions";

const OrderNow: React.FC = () => {
  const { orderType, addressDetails, location, scheduleType } = useSelector(
    (state) => state.order
  );

  const router = useRouter();

  // ==============================|| handler functions ||============================== //

  // function to validate order form & navigate to either menu or home page
  const handleOrderNow = (): void => {
    // function to validate order form
    const errorMessage: string = validateOrderForm(
      orderType,
      addressDetails,
      location,
      scheduleType
    );

    // if there is an error
    if (errorMessage) {
      // function to set message to notify user
      userNotification(errorMessage);

      // navigate to home page
      router.push("/");

      return;
    }

    // navigate to menu page, if no error
    router.push("/menu");
  };

  // ==============================|| UI ||============================== //

  return (
    <div className="fixed bottom-6 right-6">
      <Button
        variant="ghost"
        onClick={handleOrderNow}
        className="relative md:text-2xl py-2 px-3 md:py-3 md:px-6 tracking-[0em] bg-[linear-gradient(180deg,_#ffc949_0%,_#fdac1c_100%)]"
      >
        ORDER NOW
      </Button>
    </div>
  );
};

export default OrderNow;
