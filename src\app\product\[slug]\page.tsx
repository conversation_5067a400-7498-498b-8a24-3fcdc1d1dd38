import type { Metadata } from "next";
import { generateProductMetadataFromSources } from "@/lib/metadata-utils";
import {
  fetchProductDetail,
  mapProductDetailToViewModel,
} from "@/services/product-detail-api";
import ProductDetailClient from "../clientView";
import { getProductSeo } from "@/services/seo-api";
import { extractProductIdFromSlug } from "@/lib/utils/product-utils";
import { stripHtml } from "@/lib/seo-utils";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  try {
    // Extract product ID from the slug
    const { slug } = await params;
    const productId = extractProductIdFromSlug(slug);

    // Fetch product details to get the product name
    const productDetails = await fetchProductDetail(productId);

    if (
      productDetails &&
      productDetails.items &&
      productDetails.items.length > 0
    ) {
      const mappedProduct = mapProductDetailToViewModel(productDetails);
      if (mappedProduct) {
        // Build metadata using product JSON SEO first, then XML, then final fallbacks
        return generateProductMetadataFromSources(mappedProduct as any);
      }
    }
  } catch (error) {
    console.error("Error generating product metadata:", error);
  }

  // Fallback metadata
  return {
    title: "Product Details - EZeats",
  };
}

export default async function ProductDetailPage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  let product = null;
  let error = null;
  let h1 = null;

  try {
    // Extract product ID from the slug
    const productId = extractProductIdFromSlug(slug);

    const productDetails = await fetchProductDetail(productId);

    if (
      productDetails &&
      productDetails.items &&
      productDetails.items.length > 0
    ) {
      product = mapProductDetailToViewModel(productDetails);

      // Get the H1 using new fallback order: product.seo.h1 -> XML h1 -> product name
      if (product) {
        const xmlSeo = await getProductSeo(
          product.name,
          stripHtml((product as any)?.description) || ""
        );
        const fromProduct = (product as any)?.seo?.h1?.trim?.() || "";
        const fromXml = xmlSeo?.h1?.trim?.() || "";
        h1 = fromProduct || fromXml || product.name;
      }
    } else {
      error = "Product not found";
    }
  } catch (err) {
    console.error("Error loading product:", err);
    error = "Failed to load product details";
  }

  return (
    <>
      {/* Use H1 from XML if available */}
      {h1 && <h1 className="sr-only">{h1}</h1>}

      <ProductDetailClient product={product} error={error} productId={slug} />
    </>
  );
}
