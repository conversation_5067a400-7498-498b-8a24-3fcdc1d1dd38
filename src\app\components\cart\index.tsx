"use client";

import { useState, useRef } from "react";
import { usePathname, useRouter } from "next/navigation";
import Image from "next/image";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faXmark,
  faPlus,
  faMinus,
  faExclamationTriangle,
  faTrash,
} from "@fortawesome/free-solid-svg-icons";

import { useCart } from "@/lib/hooks/useCart";

import { ShoppingCart } from "../icons";
import { Button, Tabs, ConfirmationModal } from "@/app/components";

import { CartActions, CartItem } from "@/types";
import {
  decimalPlaces,
  mealBoxSize,
  productPlaceholder,
} from "@/lib/utils/constants";
import { getFormattedPrice } from "@/lib/utils/helperFunctions";
import { useSelector, dispatch } from "@/lib/store";
import {
  toggleCartDrawer,
  updateOrderStore,
  bulkUpdateOrderStore,
} from "@/lib/store/slices/order";

const Cart: React.FC = () => {
  const [confirmationModal, setConfirmationModal] = useState<boolean>(false);

  const { minSpend, currency } = useSelector((state) => state.business);
  const { cartDrawer, cartDetails, boxSize } = useSelector(
    (state) => state.order
  );

  // useRef hook used to store box size temporarily
  const boxSizeRef = useRef<number>(boxSize);

  const pathname = usePathname();
  const router = useRouter();

  // ==============================|| custom hooks ||============================== //

  // custom hook to manage cart operations
  const { updateCart, cartState } = useCart();

  // ==============================|| handler functions ||============================== //

  // function to handle cart change
  const handleCartChange = async (
    cartAction: CartActions,
    cartItem: CartItem
  ) => {
    // function to call cart API
    await updateCart(cartAction, cartItem);
  };

  // handle box size selection
  const handleBoxSizeChange = (size: number | string): void => {
    // if cart has items and box size is changing, show warning
    if (cartDetails?.itemsCount > 0 && size !== boxSize) {
      // store the updated box size in ref hook
      boxSizeRef.current = Number(size);

      // display confirmation modal
      toggleConfirmationModal();
    } else {
      // update box size in store directly if no items in cart
      dispatch(updateOrderStore({ type: "boxSize", value: size }));
    }
  };

  // function to handle modal confirmation
  const handleConfirmChange = (): void => {
    // clear cart details in store
    dispatch(
      bulkUpdateOrderStore({
        cartId: "",
        cartDetails: { subTotal: "0", itemsCount: 0, items: {} },
        boxSize: boxSizeRef.current,
      })
    );

    // close modal
    toggleConfirmationModal();
  };

  // function to handle browse menu button
  const handleBrowseMenu = (): void => {
    // close the cart drawer
    dispatch(toggleCartDrawer());

    // navigate to static menu page
    router.push("/our-menu");
  };

  // function to validate details before checkout
  const validateBeforeCheckout = (): boolean => {
    // if no items in cart
    if (cartDetails?.itemsCount <= 0) {
      return false;
    }

    // must meet minimum spend requirement (if any)
    if (minSpend > 0 && parseFloat(cartDetails?.subTotal) < minSpend) {
      return false;
    }

    // must meet box size requirement (if any)
    if (
      boxSize > 0 &&
      (cartDetails?.itemsCount < boxSize || cartDetails?.itemsCount > boxSize)
    ) {
      return false;
    }

    return true;
  };

  // handle checkout validation and navigation
  const validateCartAndProceed = (): void => {
    // verify before checkout
    if (validateBeforeCheckout()) {
      // close the cart drawer
      dispatch(toggleCartDrawer());

      // navigate to checkout page
      router?.push("/checkout");
    }
  };

  // function to handle modal cancellation
  const toggleConfirmationModal = (): void => {
    setConfirmationModal((prevState) => !prevState);
  };

  // ==============================|| UI ||============================== //

  // function to validate & display floating cart button
  const displayFloatingCart = (): boolean | React.ReactNode => {
    // verify cart drawer, pathname, and cart details
    if (
      cartDrawer ||
      cartDetails?.itemsCount <= 0 ||
      pathname?.startsWith("/checkout") ||
      pathname?.startsWith("/profile") ||
      pathname?.includes("/our-menu")
    ) {
      return false;
    }

    // render floating cart button
    return (
      <Button
        variant="primary"
        aria-label="Open cart"
        onClick={() => dispatch(toggleCartDrawer())}
        className="fixed bottom-24 right-1 z-10 text-white p-5 rounded-full flex flex-col items-center justify-center w-22 h-22 md:w-24 md:h-24"
      >
        <Image
          src="https://static.tossdown.com/images/14f80f30-18d1-41c2-ae64-8a009b7ee62b.webp"
          alt="Cart"
          width={48}
          height={48}
          priority
          className="w-40 h-40 md:w-48 md:h-48"
        />

        <span className="text-lg font-medium mt-1">
          {cartDetails?.itemsCount || 0}/{boxSize || 0}
        </span>
      </Button>
    );
  };

  return (
    <>
      {/* floating cart button */}
      {displayFloatingCart()}

      {/* cart drawer UI */}
      {cartDrawer && (
        <div
          className="fixed inset-0 bg-black/50 z-10 flex justify-end"
          onClick={() => dispatch(toggleCartDrawer())}
        >
          <div
            className="bg-white w-full max-w-md h-full flex flex-col"
            onClick={(event) => event?.stopPropagation()}
          >
            {/* cart header */}
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-2xl font-heading uppercase ">Your Cart</h2>

              <Button
                title="Close cart"
                variant="ghost"
                className="!p-0"
                onClick={() => dispatch(toggleCartDrawer())}
              >
                <FontAwesomeIcon icon={faXmark} className="!w-5 !h-5" />
              </Button>
            </div>

            {/* box size selection */}
            <div className="p-4 border-b border-gray-200">
              <div className="border border-gray-200 rounded-lg px-5 py-4">
                <p className="font-bold text-lg mb-3">Choose your box size</p>

                {/* tabs component */}
                <Tabs
                  options={Object.values(mealBoxSize)}
                  value={boxSize || mealBoxSize.SMALL}
                  size="large"
                  onClick={handleBoxSizeChange}
                />
              </div>
            </div>

            {/* cart items */}
            <div className="flex-1 overflow-y-auto">
              {!cartDetails?.itemsCount ? (
                // empty cart UI
                <div className="flex-1 flex flex-col items-center justify-center p-8 text-center">
                  <ShoppingCart className="w-16 h-16 text-gray-300 mb-4" />

                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Your cart is empty
                  </h3>

                  <p className="text-gray-500 mb-4">
                    Add some delicious meals to get started!
                  </p>

                  <Button
                    title="Browse Menu"
                    variant="primary"
                    onClick={handleBrowseMenu}
                  >
                    Browse Menu
                  </Button>
                </div>
              ) : (
                // cart items UI
                <div>
                  {Object.values(cartDetails?.items || {})?.map((item) => (
                    <div
                      key={item?.id}
                      className="flex items-center p-4 border-b border-gray-200"
                    >
                      {/* item image */}
                      <div className="w-22 h-22 relative mr-4 overflow-hidden">
                        <Image
                          src={item?.image || productPlaceholder}
                          alt={item?.name}
                          priority
                          fill
                          className="object-cover"
                        />
                      </div>

                      {/* item details */}
                      <div className="flex-1">
                        {/* item name & price */}
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="capitalize max-w-[172px] md:max-w-[195px] truncate">
                            {item?.name}
                          </h3>

                          <p className="font-medium">
                            {getFormattedPrice(
                              parseFloat(item?.price || "0"),
                              item?.currency,
                              decimalPlaces
                            )}
                          </p>
                        </div>

                        {/* item quantity & cart action buttons */}
                        <div className="flex items-center">
                          <div className="flex items-center bg-gray-100">
                            {/* remove */}
                            <Button
                              title="Remove from cart"
                              variant="ghost"
                              className="!p-2"
                              loading={
                                cartState?.loading ===
                                `${CartActions.UPDATE}-${item?.id}`
                              }
                              onClick={() =>
                                handleCartChange(CartActions.UPDATE, item)
                              }
                            >
                              <FontAwesomeIcon icon={faMinus} />
                            </Button>

                            {/* quantity */}
                            <div className="px-3 py-1">{item?.quantity}</div>

                            {/* add */}
                            <Button
                              title="Add to cart"
                              variant="ghost"
                              className="!p-2"
                              loading={
                                cartState?.loading ===
                                `${CartActions.ADD}-${item?.id}`
                              }
                              onClick={() =>
                                handleCartChange(CartActions.ADD, item)
                              }
                            >
                              <FontAwesomeIcon icon={faPlus} />
                            </Button>
                          </div>

                          {/* delete */}
                          <Button
                            title="Remove item"
                            variant="ghost"
                            className="ml-2 !p-2 !bg-gray-100"
                            loading={
                              cartState?.loading ===
                              `${CartActions.REMOVE}-${item?.id}`
                            }
                            onClick={() =>
                              handleCartChange(CartActions.REMOVE, item)
                            }
                          >
                            <FontAwesomeIcon icon={faTrash} />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* cart footer */}
            {cartDetails?.itemsCount > 0 && (
              <div className="border-t border-gray-200 p-4 bg-white">
                {/* box size warning */}
                {boxSize > 0 && cartDetails?.itemsCount < boxSize && (
                  <div className="mb-2 p-2 bg-yellow-50 border border-yellow-200 flex items-start">
                    <FontAwesomeIcon
                      icon={faExclamationTriangle}
                      className="text-yellow-500 mr-2"
                    />

                    <p className="text-sm text-yellow-700">
                      Your meal box requires {boxSize} items. Please add{" "}
                      {boxSize - cartDetails.itemsCount} more{" "}
                      {boxSize - cartDetails.itemsCount === 1
                        ? "item"
                        : "items"}
                      .
                    </p>
                  </div>
                )}

                {/* minimum spend warning */}
                {minSpend > 0 &&
                  parseFloat(cartDetails.subTotal) < minSpend && (
                    <div className="mb-2 p-2 bg-yellow-50 border border-yellow-200 flex items-start">
                      <FontAwesomeIcon
                        icon={faExclamationTriangle}
                        className="text-yellow-500 mr-2"
                      />

                      <p className="text-sm text-yellow-700">
                        Minimum order amount is{" "}
                        {getFormattedPrice(minSpend, currency, decimalPlaces)}.
                        You are{" "}
                        {getFormattedPrice(
                          minSpend - parseFloat(cartDetails.subTotal),
                          currency,
                          decimalPlaces
                        )}{" "}
                        short.
                      </p>
                    </div>
                  )}

                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium">Subtotal</p>

                    <p className="text-xl font-bold">
                      {getFormattedPrice(
                        parseFloat(cartDetails.subTotal),
                        currency,
                        decimalPlaces
                      )}
                    </p>
                  </div>

                  <Button
                    variant="primary"
                    disabled={!validateBeforeCheckout()}
                    className={"!text-[16px] !font-bold py-3 px-8 rounded-full"}
                    onClick={validateCartAndProceed}
                  >
                    CHECKOUT
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* box size change confirmation modal */}
      {confirmationModal && (
        <ConfirmationModal
          open={confirmationModal}
          title="Change Box Size"
          message="Changing your box size will clear your current cart. Do you want to continue?"
          onConfirm={handleConfirmChange}
          onCancel={toggleConfirmationModal}
        />
      )}
    </>
  );
};

export default Cart;
