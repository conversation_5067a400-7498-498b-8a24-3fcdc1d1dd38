import { Metadata } from "next";

import ClientView from "./clientView";
import { PageSEODetails } from "@/types";
import { createMetadata } from "@/lib/utils/helperFunctions";

// generate metadata for the checkout page (SSR)
export const generateMetadata = async (): Promise<Metadata> => {
  // checkout SEO details
  const checkoutSEODetails: PageSEODetails = {
    pagetitle: "Checkout - EZeats Fresh Meal Delivery and Tiffin Service",
    h1: "Complete Your Order - Fresh Meals Delivered",
    desc: "Complete your secure checkout for fresh, delicious meals delivered to your door. Fast, safe, and convenient meal delivery service in GTA.",
    keywords:
      "checkout, secure payment, meal delivery, food delivery, order online, EZeats, GTA delivery",
  };

  // function call to create Next.js metadata from SEO data
  return createMetadata(checkoutSEODetails);
};

// checkout page component
const Checkout: React.FC = () => {
  return (
    <main>
      {/* hidden h1 for SEO */}
      <h1 className="sr-only">Complete Your Order - Fresh Meals Delivered</h1>

      {/* client side UI */}
      <ClientView />
    </main>
  );
};

export default Checkout;
