import React from "react";
import { RatingProps } from "../index.types";

const Rating: React.FC<RatingProps> = ({ rating }) => {
  // create an array of length 5 (for 5 stars)
  const stars: React.ReactElement[] = Array.from({ length: 5 }, (_, index) => {
    // calculate how filled this star should be (0 to 1)
    const fillPercentage: number = Math?.max(0, Math.min(1, rating - index));

    return (
      <div key={index} className="relative inline-block w-5 h-5 md:w-6 md:h-6">
        {/* empty star (background) */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke={"#d1d5db"}
          className="absolute top-0 left-0 w-full h-full"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
          />
        </svg>

        {/* filled star (foreground with clip-path for partial fill) */}
        {fillPercentage > 0 && (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill={"#f97316"}
            stroke={"#f97316"}
            className="w-full h-full"
            style={{
              clipPath: `inset(0 ${100 - fillPercentage * 100}% 0 0)`,
            }}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
            />
          </svg>
        )}
      </div>
    );
  });

  // ==============================|| UI ||============================== //

  return <div className={"flex"}>{stars}</div>;
};

export default Rating;
