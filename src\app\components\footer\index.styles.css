.footer-section {
  background: #000000;
  color: #fff;
  float: left;
  width: 100%;
  padding: 56px 15px 54px 15px;
}

.footer-data-box {
  display: grid;
  width: 100%;
  grid-template-columns: 1.4fr 1fr 1fr 1fr 1fr;
  grid-template-rows: auto;
  grid-template-areas: ". . . .";
  gap: 30px 30px;
}

.footer-detail-box-one {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.footer-detail-box-one figure {
  margin: 0px !important;
  width: 131px !important;
  padding-top: 6px;
}

.footer-detail-box-one figure img {
  width: 100%;
}

.footer-detail-box-two {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.footer-detail-box-heading {
  position: relative;
  padding-bottom: 14px;
}

.footer-detail-box-heading h3 {
  margin: 0px !important;
  font-family: Anton;
  font-size: 18px;
  font-weight: 400;
  line-height: 32px;
  letter-spacing: 0em;
  color: #ffffff;
  text-transform: uppercase;
}

.footer-detail-box-content-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0px !important;
  margin: 0px !important;
}

.footer-detail-box-content-list li {
  list-style: none;
  display: flex;
  align-items: center;
  padding-bottom: 14px;
}

.footer-detail-box-content-list li:last-child {
  padding-bottom: 0px;
}

.footer-detail-box-content-list li a {
  font-family: Poppins;
  font-size: 13px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0em;
  text-decoration: none;
  color: #fff;
  text-transform: uppercase;
}

.footer-detail-box-content-list li a:hover {
  color: #fff;
}

.footer-contact-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0px !important;
  margin: 0px !important;
}

.footer-contact-list li {
  list-style: none;
  display: flex;
  padding-bottom: 14px;
}

.footer-contact-list li:last-child {
  padding-bottom: 0px;
}

.footer-contact-list li i {
  padding-right: 6px;
  font-size: 16px;
  line-height: 18px;
  color: #231f20;
}

.footer-contact-list li i:before {
  color: #fff !important;
}

.footer-contact-list li a {
  font-family: Poppins;
  font-size: 13px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0em;
  color: #fff;
  text-decoration: none;
}

.footer-contact-list li a:hover {
  color: #fff;
}

.footer-socail-links {
  display: flex;
  align-items: center;
  padding-top: 15px;
}

.footer-socail-links a {
  background: rgba(255, 255, 255, 0.2);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  border-radius: 50%;
  justify-content: center;
  margin-right: 10px;
}

.footer-socail-links a i {
  color: #ffffff;
  font-size: 16px;
}

@media (max-width: 1024px) {
  .footer-data-box {
    grid-template-columns: 1.4fr 1.5fr 1.2fr;
    grid-template-areas: ". . .";
  }
}

@media (max-width: 768px) {
  .footer-data-box {
    grid-template-columns: 1.6fr 1fr;
    grid-template-areas: ". .";
  }
}

@media (max-width: 540px) {
  .footer-section {
    padding: 40px 15px 40px 15px;
  }

  .footer-data-box {
    grid-template-columns: 1fr;
    grid-template-areas: ".";
    gap: 30px 0px;
  }

  .footer-detail-box-one {
    justify-content: center;
    align-items: center;
  }

  .footer-detail-box-one p {
    padding-right: 0px;
    text-align: center;
  }

  .footer-detail-box-heading {
    padding-bottom: 0px;
  }

  .footer-contact-list {
    padding-top: 15px !important;
  }

  .footer-detail-box-content-list {
    padding-top: 15px !important;
  }

  .footer-socail-links {
    justify-content: center;
    align-items: center;
  }

  .panel {
    display: none;
    overflow: hidden;
    transition: 0.5s ease-out;
    text-align: left;
  }

  .footer-detail-box-heading.accordion:after {
    content: "\002B";
    color: #fff;
    font-family: "revicons";
    font-size: 26px;
    position: absolute;
    height: 100%;
    display: flex;
    align-items: center;
    margin: 0px;
    top: 0px;
    right: 0px;
  }

  .footer-detail-box-heading.active:after {
    content: "\2212" !important;
    color: #fff;
    font-family: "revicons";
    font-size: 21px;
    position: absolute;
    height: 100%;
    display: flex;
    align-items: center;
    margin: 0px;
    top: 0px;
    right: 0px;
  }
}
