// services/seo-api.tsx

import { fetchSeoData, type SeoData } from "@/services/seo-service";

/**
 * Gets SEO data for a product detail page, replacing placeholders
 */
export async function getProductSeo(
  productName: string,
  productDescription: string = ""
): Promise<SeoData | null> {
  const seoData = await fetchSeoData();
  const detailPage = seoData["detail"];

  if (!detailPage) {
    console.warn("No SEO data found for product detail page");
    return null;
  }

  // Clone the SEO data
  const productSeo: SeoData = {};

  // Only include fields that exist in the XML and have content
  const replacePlaceholders = (val?: string) =>
    typeof val === "string"
      ? val
          .replace(/<product_name>/g, productName)
          .replace(/<product_desc>/g, productDescription)
      : undefined;

  const srcSeo = detailPage.seo || {};
  const srcGen = detailPage.generated || {};

  if (srcSeo.pagetitle || srcGen.pagetitle) {
    productSeo.pagetitle =
      replacePlaceholders(srcSeo.pagetitle) ||
      replacePlaceholders(srcGen.pagetitle);
  }

  if (srcSeo.h1 || srcGen.h1) {
    productSeo.h1 =
      replacePlaceholders(srcSeo.h1) || replacePlaceholders(srcGen.h1);
  }

  if (srcSeo.desc || srcGen.desc) {
    productSeo.desc =
      replacePlaceholders(srcSeo.desc) || replacePlaceholders(srcGen.desc);
  }

  if (srcSeo.keywords || srcGen.keywords) {
    productSeo.keywords =
      replacePlaceholders(srcSeo.keywords) ||
      replacePlaceholders(srcGen.keywords);
  }

  return productSeo;
}
