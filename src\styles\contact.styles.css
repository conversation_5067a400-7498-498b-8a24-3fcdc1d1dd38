.inner-custom-heading {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: center;
  padding-bottom: 50px;
}

.inner-custom-heading h3 {
  font-family: Anton;
  text-align: center;
  padding: 0px 25px;
  margin: 0px;
  font-size: 70px;
  font-weight: 400;
  line-height: 96px;
  letter-spacing: 0em;
  color: #000;
  margin-bottom: 0px;
  text-transform: uppercase;
  position: relative;
}

.inner-custom-heading {
  text-align: center;
}

.contact_container {
  max-width: 1140px;
  margin: 0px auto;
}

/* contact section one start */
.contact_section_main {
  display: inline-block;
  width: 100%;
  float: left;
}
.contact_section_one {
  float: left;
  display: inline-block;
  width: 100%;
  padding: 65px 15px 90px 15px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(180deg, #ededed 0%, rgba(217, 217, 217, 0) 48.5%);
}
.contact_section_one figure {
  position: absolute;
  bottom: 0px;
  left: 0px;
}

.contact_section_one_parent {
  display: grid;
  gap: 30px;
  align-items: center;
}

.contact_section_one_box {
  display: grid;
  grid-template-columns: 1fr;
  width: 100%;
  gap: 30px 100px;
  align-items: flex-start;
}

.contact_section_one_box_heading {
  display: grid;
  grid-template-columns: auto;
  width: 100%;
}

.contact_section_one_box_heading h3 {
  color: #000;
  font-family: Cormorant Garamond;
  font-size: 44px;
  font-style: normal;
  font-weight: 700;
  line-height: 57px;
  margin: 0px;
  padding-bottom: 14px;
}

.contact_section_one_box_heading p {
  color: #000;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 25px;
}
.contact_section_one_box form {
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 9;
}
.contact_section_one_box form img {
  display: block;
}
.contact_page_form_content {
  display: grid;
  width: 100%;
  max-width: 570px;
  margin: auto;
  grid-template-rows: auto;
}
.contact_page_form_content h3 {
  color: #000;
  font-family: Cormorant Garamond;
  font-size: 34px;
  font-style: normal;
  font-weight: 700;
  line-height: 45px;
  margin: 0px;
  text-align: left;
  padding-bottom: 20px;
}

.contact_us_form_fields {
  display: grid;
  width: 100%;
  grid-template-columns: auto;
  grid-template-rows: auto;
  gap: 7px 0px;
  padding-bottom: 20px;
  position: relative;
}

.contact_us_form_fields span {
  color: #000;
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 18px;
  text-transform: uppercase;
  position: absolute;
  top: -9px;
  left: 22px;
  padding: 0px 12px;
  z-index: 9;
}
.contact_us_form_fields span:before {
  position: absolute;
  content: "";
  width: 100%;
  border: 1px solid #fff;
  left: 0px;
  top: 9px;
  z-index: -1;
}

.contact_us_form_fields input {
  border: 1px solid #212121;
  background: #fff;
  height: 48px;
  padding: 0px 15px 0px 15px;
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
  width: 100%;
  border-radius: 0px;
}
.contact_us_form_fields textarea {
  border: 1px solid #212121;
  background: #fff;

  padding: 15px 15px 15px 15px;
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
  width: 100%;
  border-radius: 0px;
}

.contact_us_form_fields label.error {
  color: #cc0000;
  text-transform: capitalize;
  margin: 0px !important;
  font-style: normal;
  font-weight: normal;
  font-size: 13px;
  line-height: 11px;
}

.contact_us_form_submit_btn {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
}

.contact_us_form_submit_btn button {
  border: 1px solid #ffa800;
  background: #ffa800;
  border-radius: 0px;
  color: #fff;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 47px;
  cursor: pointer;
}
.contact_us_form_submit_btn button i.far.fa-paper-plane {
  margin-right: 10px;
}
img.left-img {
  position: absolute;
  width: 166px;
  left: 100px;
  height: 333px;
  top: 250px;
}
img.right-img {
  top: 30px;
  position: absolute;
  right: 0px;
  width: 429px;
  height: 525px;
}

@media (max-width: 1200px) {
  img.right-img {
    width: 274px;
    height: unset;
  }
  .inner-custom-heading {
    padding-bottom: 38px;
  }

  .inner-custom-heading h3 {
    font-size: 40px;
    line-height: 59px;
  }
  img.left-img {
    width: 84px;
    height: unset;
    left: 30px;
  }
}

@media (max-width: 1024px) {
  .contact_section_one {
    padding: 50px 15px 50px 15px;
  }

  .contact_section_one_box {
    gap: 30px 50px;
  }
}

@media (max-width: 768px) {
  .inner-custom-heading h3 {
    font-size: 28px;
    line-height: 38px;
  }
  .inner-custom-heading {
    padding-bottom: 38px;
  }
  img.right-img {
    display: none;
  }
  img.left-img {
    display: none;
  }
  .contact_section_one_box_heading h3 {
    font-size: 28px;
    line-height: 36px;
  }
  .contact_section_one_box_heading p {
    font-size: 14px;
    line-height: 22px;
  }
  .contact_section_one_box {
    grid-template-columns: 1fr;
  }
  .contact_section_one_box_heading {
    text-align: center;
  }

  .contact_section_one figure {
    bottom: -16px;
    z-index: 1;
  }

  .contact_page_form_content h3 {
    font-size: 23px;
    line-height: 35px;
  }
}
div#recaptcha {
  padding-bottom: 12px;
}
@media (max-width: 400px) {
  div#recaptcha {
    transform: scale(0.6);
    transform-origin: 0 0;
    padding-bottom: 0;
  }
}
.contact_ab {
  width: 100%;
  float: left;
  transform: translatey(10px);
  width: 100%;
  object-fit: cover;
}
.contact_ab img {
  width: 100%;
  object-fit: cover;
}
