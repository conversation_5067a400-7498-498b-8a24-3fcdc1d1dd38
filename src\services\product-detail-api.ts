import { Branch, ProductDetail, RequestHelperResponse } from "@/types";
import { callProductDetailAbstractedAPI } from "@/lib/apiConfigs";

// function to fetch product detail using abstracted API
export async function fetchProductDetail(
  productId: string | number
): Promise<ProductDetail | null> {
  try {
    // call the abstracted API
    const response = await callProductDetailAbstractedAPI(productId.toString());

    // return the product detail data
    return response.data as ProductDetail;
  } catch (error) {
    console.error("Error fetching product details:", error);
    throw error;
  }
}

// function to fetch product by slug
export async function fetchProductBySlug(
  slug: string
): Promise<ProductDetail | null> {
  try {
    // extract product ID from slug using the utility function
    const { extractProductIdFromSlug } = await import(
      "@/lib/utils/product-utils"
    );
    const productId = extractProductIdFromSlug(slug);
    return await fetchProductDetail(productId);
  } catch (error) {
    console.error("Error fetching product by slug:", error);
    throw error;
  }
}
