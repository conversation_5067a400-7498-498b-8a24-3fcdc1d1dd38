"use client";

import React, { useEffect } from "react";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faXmark } from "@fortawesome/free-solid-svg-icons";

import { Button } from "@/app/components";
import { useSelector, dispatch } from "@/lib/store";
import { hideToast, ToastType } from "@/lib/store/slices/toast";

const Toast: React.FC = () => {
  const { display, type, message } = useSelector((state) => state.toast);

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // declare timer
    let timer: NodeJS.Timeout;

    // auto dismiss toast after 1.5 seconds
    if (display) {
      timer = setTimeout(() => {
        // hide toast
        dispatch(hideToast());
      }, 1500);
    }

    // clean up timer on unmount
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [display]);

  // ==============================|| handler functions ||============================== //

  // function to return toast variants
  const getToastStyles = (type: ToastType): string => {
    const baseClasses =
      "fixed font-medium text-sm left-1/2 -translate-x-1/2 z-20 transform transition-all duration-300 ease-in-out shadow-lg";

    // variants for each toast type
    const variants: Record<ToastType, string> = {
      success:
        "bottom-12 bg-green-500 text-white p-2 flex items-center rounded-lg",
      warning:
        "bottom-12 bg-yellow-500 text-white p-2 flex items-center rounded-lg",
      info: "bottom-12 bg-blue-500 text-white p-2 flex items-center rounded-lg",
      error:
        "top-1/2 -translate-y-1/2 w-[290px] bg-[#f5c6cb] text-[#721c24] leading-4 p-[14px_20px] rounded-[25px] flex flex-col items-center",
    };

    return `${baseClasses} ${variants[type]}`;
  };

  // function to close the toast
  const handleClose = (): void => {
    // hide toast
    dispatch(hideToast());
  };

  // ==============================|| UI ||============================== //

  return (
    display &&
    type && (
      <div className={getToastStyles(type)}>
        {type === "error" ? (
          <>
            <span className="mb-3 text-center">{message}</span>

            <Button
              variant="primary"
              className="!bg-[#721c24] text-white !px-4 !py-1 rounded-full text-xs font-medium"
              onClick={handleClose}
            >
              OK
            </Button>
          </>
        ) : (
          <>
            <span className="mr-4">{message}</span>

            <Button
              variant="ghost"
              aria-label="Close toast"
              className="text-white !p-0"
              onClick={handleClose}
            >
              <FontAwesomeIcon icon={faXmark} />
            </Button>
          </>
        )}
      </div>
    )
  );
};

export default Toast;
