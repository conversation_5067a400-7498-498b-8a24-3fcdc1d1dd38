import { createSlice } from "@reduxjs/toolkit";

// ==============================|| slice type ||============================== //

export type ToastType = "success" | "error" | "warning" | "info";

export interface ToastState {
  display: boolean;
  type: ToastType | null;
  message: string;
}

// ==============================|| initial state ||============================== //

const initialState: ToastState = {
  display: false,
  type: null,
  message: "",
};

// ==============================|| toast slice ||============================== //

const Toast = createSlice({
  name: "toast",
  initialState,
  reducers: {
    displayToast: (state, action) => {
      return { ...state, ...action.payload };
    },
    hideToast: (state) => {
      return { ...state, display: false, type: null, message: "" };
    },
  },
});

export default Toast.reducer;

export const { displayToast, hideToast } = Toast.actions;
