import {
  faHouse,
  faUtensils,
  faKitchenSet,
  faPhone,
} from "@fortawesome/free-solid-svg-icons";
import { Calendar, Subscription } from "@/app/components/icons";

import {
  NavMenuItem,
  PageSEODetails,
  ScheduleTypes,
  ScheduleTypesOptions,
} from "@/types";

// ==============================|| constants ||============================== //

export const businessName = "EZeats";
export const decimalPlaces = 2;
export const cookiesKey = { userDetails: "tdUDetails", cart: "EZDetails" };

// checkout scripts DOM element IDs
export const checkoutScripts = {
  businessInfo: "business-info",
  checkoutMain: "checkout-main",
  checkoutChunk: "checkout-chunk",
  checkoutCSS: "checkout-css",
};

// profile scripts DOM element IDs
export const profileScripts = {
  businessInfo: "profile-business-info",
  profileMain: "profile-main",
  profileChunk: "profile-chunk",
  profileCSS: "profile-css",
};

// header logo
export const headerLogo =
  "https://static.tossdown.com/images/a7cfb016-7b8d-4e9e-85f7-e0041556b62b.webp";

// product placeholder logo
export const productPlaceholder =
  "https://static.tossdown.com/logos/0b5194b4-5472-4ca3-9e13-d3c5aaffd7f5.png";

// default SEO data (fallback)
export const defaultSEO: PageSEODetails = {
  pagetitle: "EZeats - Fresh Meal Delivery Service",
  h1: "Fresh and affordable meal delivery service in GTA. Halal Indian vegetarian and non-vegetarian meals delivered to your door.",
  desc: "",
  keywords: "",
};

// request timeout in milliseconds (50 seconds)
export const requestTimeout = 50000;

// navigation menu
export const menuItems: NavMenuItem[] = [
  { label: "HOME", url: "/", icon: faHouse },
  { label: "MENU", url: "/our-menu", icon: faUtensils },
  { label: "OUR KITCHEN", url: "/our-kitchen", icon: faKitchenSet },
  { label: "CONTACT US", url: "/contact-us", icon: faPhone },
];

export const scheduleTypesOptions: ScheduleTypesOptions[] = [
  { label: "One time", value: ScheduleTypes.LATER, icon: Calendar },
  {
    label: "Weekly subscription",
    value: ScheduleTypes.WEEKLY,
    icon: Subscription,
  },
  {
    label: "Bi-weekly subscription",
    value: ScheduleTypes.BIWEEKLY,
    icon: Subscription,
  },
];

export const mealBoxSize = {
  SMALL: 6,
  MEDIUM: 12,
};

export const initialSchedule = { date: { label: "", value: "" }, time: "" };

export const initialUserDetails = {
  name: "",
  email: "",
  phone: "",
  userId: "",
  tdUserId: "",
  authToken: "",
  renewAuthToken: "",
  paymentDetails: {
    stripe: { stripeCustomerId: "", selectedCardId: "", saveCard: false },
  },
};

export const userAuthTypes = {
  LOGIN: "Login",
  SIGN_UP: "Sign up",
  RESET_PASSWORD: "Reset Password",
};

export const dateTimeFormats = {
  DATE: "YYYY-MM-DD",
  DAY: "dddd",
  MONTH_DATE: "MMM DD, YYYY",
};

// XML parser configuration
export const xmlParserOptions = {
  ignoreAttributes: false,
  attributeNamePrefix: "@_",
  textNodeName: "#text",
  parseAttributeValue: true,
  trimValues: true,
};
