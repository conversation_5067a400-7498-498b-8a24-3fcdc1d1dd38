import Link from "next/link";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

import { NavigationMenuProps } from "../index.types";
import { NavMenuItem } from "@/types";
import { menuItems } from "@/lib/utils/constants";

const NavigationMenu: React.FC<NavigationMenuProps> = ({
  className,
  onNavigate,
  displayIcon,
}) => {
  return menuItems?.map((item: NavMenuItem) => (
    <Link
      key={item?.label}
      href={item?.url}
      className={className}
      onClick={onNavigate}
    >
      <span className="flex items-center gap-3">
        {/* display icon only on mobile */}
        {displayIcon && (
          <FontAwesomeIcon icon={item?.icon} className="icon-size" />
        )}

        {/* display label */}
        <span>{item?.label}</span>
      </span>
    </Link>
  ));
};

export default NavigationMenu;
