import { configureStore } from "@reduxjs/toolkit";
import { combineReducers } from "@reduxjs/toolkit";
import {
  useDispatch as useAppDispatch,
  useSelector as useAppSelector,
  TypedUseSelectorHook,
} from "react-redux";
import { persistStore, persistReducer, Persistor } from "redux-persist";
import storage from "redux-persist/lib/storage";

import Order from "./slices/order";
import Business from "./slices/business";
import Toast from "./slices/toast";

// ==============================|| REDUX PERSIST CONFIG ||============================== //

// combine reducers
const rootReducer = combineReducers({
  business: Business,
  order: persistReducer(
    {
      key: "order",
      storage,

      // exclude keys from persistence
      blacklist: ["orderTypes", "cartDrawer", "userDetails"],
    },
    Order
  ),
  toast: Toast,
});

// ==============================|| REDUX - MAIN STORE ||============================== //

const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ["persist/PERSIST"],
      },
    }),
});

// create persister
export const persistor: Persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

const { dispatch } = store;

const useDispatch = () => useAppDispatch<AppDispatch>();
const useSelector: TypedUseSelectorHook<RootState> = useAppSelector;

export { store, dispatch, useSelector, useDispatch };
