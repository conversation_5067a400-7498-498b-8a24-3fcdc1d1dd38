"use client";

import React from "react";

import { Select } from "@/app/components";
import { FieldError } from "@/app/components";
import { ScheduleTypeProps } from "../index.types";
import { scheduleTypesOptions } from "@/lib/utils/constants";

const ScheduleType: React.FC<ScheduleTypeProps> = ({
  scheduleType,
  setScheduleType,
  orderType,
}) => {
  // ==============================|| handler functions ||============================== //

  // function to handle schedule type change
  const handleScheduleTypeChange = (
    event: React.ChangeEvent<HTMLSelectElement>
  ) => {
    // update local state with the selected schedule type
    setScheduleType({
      type: event?.target?.value || "",
      error: "",
    });
  };

  // ==============================|| UI ||============================== //

  return (
    <div className="relative flex-1 dropdown_toggle">
      <Select
        title="Schedule type"
        placeholder={`Schedule ${orderType}`}
        value={scheduleType.type}
        options={scheduleTypesOptions}
        className="h-[61px] max-[540px]:h-[50px]"
        onChange={handleScheduleTypeChange}
      />

      <FieldError errorMessage={scheduleType?.error} />
    </div>
  );
};

export default ScheduleType;
