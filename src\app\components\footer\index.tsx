import Image from "next/image";
import Link from "next/link";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faFacebookF,
  faInstagram,
  faTiktok,
} from "@fortawesome/free-brands-svg-icons";
import moment from "moment-timezone";

import ClientView from "./clientView";
import "./index.styles.css";

const Footer = () => {
  return (
    <>
      <section className="footer-section">
        <div className="static_container">
          <div className="footer-data-box">
            <div className="footer-data-box-detail ">
              <div className="footer-detail-box-one">
                <div className="flex items-center mb-5">
                  <figure className="pb-0 mb-0">
                    <Image
                      src="https://images-beta.tossdown.com/site/9e313078-7418-407e-aa05-fd244df31d5a.webp"
                      alt="Business logo"
                      width={200}
                      height={60}
                      className="object-contain"
                    />
                  </figure>

                  <Image
                    src="https://static.tossdown.com/site/9cae0094-a2b4-41b0-a2e9-3d038f40eed0.webp"
                    alt="HMA logo"
                    width={65}
                    height={35}
                    className="ml-5"
                  />
                </div>

                <p>Your affordable meal solution!</p>

                <div className="footer-socail-links">
                  <Link
                    href="https://www.facebook.com/share/1ESo81z7ga/?mibextid=wwXIfr"
                    target="_blank"
                  >
                    <FontAwesomeIcon icon={faFacebookF} className="icon-size" />
                  </Link>{" "}
                  <Link
                    href="https://www.instagram.com/ezeats.ca?igsh=dG42ZTYzYm9ldHpm"
                    target="_blank"
                  >
                    <FontAwesomeIcon icon={faInstagram} className="icon-size" />
                  </Link>{" "}
                  <Link
                    href="https://www.tiktok.com/@ezeats.ca?_t=ZS-8vx9kdn0LAY&_r=1"
                    target="_blank"
                  >
                    <FontAwesomeIcon icon={faTiktok} className="icon-size" />
                  </Link>
                </div>
              </div>
            </div>

            <div className="footer-data-box-detail">
              <div className="footer-detail-box-two">
                <div className="footer-detail-box-heading accordion modtitle">
                  <h3 className="footer-collapse-icons">Quick Links</h3>
                </div>
                <div className="modcontent panel">
                  <ul className="footer-detail-box-content-list">
                    <li>
                      <Link href="/">HOME</Link>
                    </li>
                    <li>
                      <Link href="/our-menu">OFFERED IN MENU</Link>
                    </li>
                    <li>
                      <Link href="/our-kitchen">OUR KITCHEN</Link>
                    </li>
                    <li>
                      <Link href="/contact-us">CONTACT US</Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="footer-data-box-detail">
              <div className="footer-detail-box-two">
                <div className="footer-detail-box-heading accordion modtitle">
                  <h3 className="footer-collapse-icons">Useful Links</h3>
                </div>
                <div className="modcontent panel">
                  <ul className="footer-detail-box-content-list">
                    <li>
                      <Link href="/terms-and-conditions">
                        Terms &amp; Conditions
                      </Link>
                    </li>
                    <li>
                      <Link href="/privacy-policy">Privacy Policy</Link>
                    </li>
                    <li>
                      <Link href="/blog">Blogs</Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="footer-data-box-detail">
              <div className="footer-detail-box-two">
                <div className="footer-detail-box-heading accordion modtitle">
                  <h3 className="footer-collapse-icons">site links</h3>
                </div>
                <div className="modcontent panel">
                  <ul className="footer-detail-box-content-list">
                    <li>
                      <Link href="/vegetarian-meal-delivery">
                        Vegetarian Meal Delivery
                      </Link>
                    </li>
                    <li>
                      <Link href="/halal-meal-delivery">
                        Halal Meal Delivery
                      </Link>
                    </li>
                    <li>
                      <Link href="/tiffin-service-near-me">
                        Tiffin Service Near Me
                      </Link>
                    </li>
                    <li>
                      <Link href="/butter-chicken-tiffin-service-in-gta">
                        Butter Chicken Tiffin Service in GTA
                      </Link>
                    </li>
                    <li>
                      <Link href="/tiffin-service-in-gta">
                        Tiffin Service in GTA
                      </Link>
                    </li>
                    <li>
                      <Link href="/indian-food-delivery-service">
                        Indian Food Delivery Service
                      </Link>
                    </li>
                    <li>
                      <Link href="/quick-meals-for-busy-summers">
                        Quick Meals for Busy Summers
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="footer-data-box-detail">
              <div className="footer-detail-box-two ">
                <div className="footer-detail-box-heading accordion modtitle">
                  <h3 className="footer-collapse-icons">Contact Us</h3>
                </div>

                {/* client side UI */}
                <ClientView />
              </div>
            </div>
          </div>
        </div>
      </section>

      <div className="copyright_footer">
        <div
          className="w-full text-center py-2.5 bg-black"
          style={{ borderTop: "1px solid #FFFFFF1A" }}
        >
          <p className="text-sm text-white mb-0">
            © {moment()?.year()} EZeats.
          </p>
        </div>
      </div>
    </>
  );
};

export default Footer;
