import { NextResponse } from "next/server";

import { callProductsAPI } from "@/lib/apiConfigs";

// get products API route handler
export async function GET() {
  // function to call the get products API
  const { data, error } = await callProductsAPI();

  // verify if error
  if (error) {
    // return error response
    return NextResponse.json({ data: [], meta: null, error }, { status: 500 });
  }

  // return success response
  return NextResponse.json({ data, meta: null, error: null }, { status: 200 });
}
