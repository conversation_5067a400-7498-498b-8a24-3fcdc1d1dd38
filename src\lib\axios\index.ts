import axios, {
  AxiosInstance,
  AxiosResponse,
  AxiosError,
  InternalAxiosRequestConfig,
} from "axios";

import { requestTimeout } from "@/lib/utils/constants";

// create axios instance with base configuration
const axiosClient: AxiosInstance = axios?.create({
  baseURL: process.env.NEXT_PUBLIC_BASE_URL,
  timeout: requestTimeout,
});

// request interceptor
axiosClient.interceptors.request.use((config: InternalAxiosRequestConfig) => {
  if (process.env.NEXT_PUBLIC_ENVIRONMENT === "development") {
    console.log("🚀 API Request:", {
      method: config.method?.toUpperCase(),
      url: config.url,
      baseURL: config.baseURL,
      headers: config.headers,
      data: config.data,
      timestamp: new Date().toISOString(),
    });
  }

  return config;
});

// response interceptor
axiosClient.interceptors.response.use(
  (response: AxiosResponse) => {
    if (process.env.NEXT_PUBLIC_ENVIRONMENT === "development") {
      console.log("✅ API Response:", {
        status: response.status,
        statusText: response.statusText,
        url: response.config.url,
        method: response.config.method?.toUpperCase(),
        data: response.data,
        timestamp: new Date().toISOString(),
      });
    }

    return response;
  },
  (error: AxiosError) => {
    if (process.env.NEXT_PUBLIC_ENVIRONMENT === "development") {
      console.error("❌ API Error:", {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        url: error.config?.url,
        method: error.config?.method?.toUpperCase(),
        data: error.response?.data,
        timestamp: new Date().toISOString(),
      });
    }

    return Promise.reject(error);
  }
);

export default axiosClient;
