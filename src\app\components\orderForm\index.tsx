"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

import { <PERSON><PERSON> } from "@/app/components";
import { Schedule } from "@/app/components";
import { OrderType } from "@/app/components";
import { ScheduleType } from "@/app/components";
import { PickupAddress } from "@/app/components";
import { DeliveryAddress } from "@/app/components";

import { SelectedLocationState, ScheduleTypeState } from "../index.types";
import { AddressDetails, OrderTypes, RequestStateData } from "@/types";
import { cookiesKey } from "@/lib/utils/constants";
import { updateCookies } from "@/lib/utils/helperFunctions";
import { useSelector, dispatch } from "@/lib/store";
import { bulkUpdateOrderStore } from "@/lib/store/slices/order";

const OrderForm = () => {
  const [selectedLocation, setSelectedLocation] =
    useState<SelectedLocationState>({
      location: null,
      error: "",
    });
  const [deliveryAddressState, setDeliveryAddressState] = useState<
    RequestStateData<AddressDetails>
  >({
    data: {
      address: "",
      street: "",
      area: "",
      city: "",
      country: "",
      postalCode: "",
      location: null,
    },
    loading: false,
    error: "",
  });
  const [scheduleType, setScheduleType] = useState<ScheduleTypeState>({
    type: "",
    error: "",
  });
  const [toggleScheduleModal, setToggleScheduleModal] =
    useState<boolean>(false);

  const {
    orderTypes,
    location,
    orderType,
    scheduleType: persistedScheduleType,
    addressDetails,
  } = useSelector((state) => state.order);

  const router = useRouter();

  // ==============================|| useEffect hook ||============================== //

  // populate local states from redux store on component mount
  useEffect(() => {
    // populate delivery address from persisted store
    if (addressDetails?.address) {
      setDeliveryAddressState((prevState) => ({
        ...prevState,
        data: addressDetails,
      }));
    }

    // populate selected location from persisted store
    if (location) {
      setSelectedLocation((prevState) => ({ ...prevState, location }));
    }

    // populate schedule type from persisted store
    if (persistedScheduleType) {
      setScheduleType((prevState) => ({
        ...prevState,
        type: persistedScheduleType,
      }));
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // ==============================|| handler functions ||============================== //

  // function to validate form & persist form details to store
  const validateOrderForm = (): void => {
    // verify delivery details and set error
    if (orderType === OrderTypes.DELIVERY) {
      /**
       * if => there is an error, return
       * else if => there is no address, lat, or lng, return
       */
      if (deliveryAddressState?.error) {
        return;
      } else if (
        !deliveryAddressState?.data?.address ||
        !deliveryAddressState?.data?.location?.lat ||
        !deliveryAddressState?.data?.location?.lng
      ) {
        // update error state
        setDeliveryAddressState((preState) => {
          return { ...preState, error: "Please enter a delivery address" };
        });

        return;
      }
    }

    // verify pickup address and set error
    if (
      (orderType === OrderTypes.PICKUP && !selectedLocation.location) ||
      selectedLocation?.error
    ) {
      // update error state
      setSelectedLocation((prevState) => {
        return { ...prevState, error: "Please select a pickup location" };
      });

      return;
    }

    // verify schedule type and set error
    if (!scheduleType?.type || scheduleType?.error) {
      // update error state
      setScheduleType((prevState) => {
        return { ...prevState, error: "Select a schedule option" };
      });

      return;
    }

    // save order details to persisted store
    dispatch(
      bulkUpdateOrderStore({
        location: selectedLocation?.location,
        scheduleType: scheduleType?.type,
        addressDetails: deliveryAddressState?.data,
      })
    );

    // set data in cookies
    updateCookies(cookiesKey.cart, {
      branchId: selectedLocation?.location?.id || "",
    });

    // open schedule modal
    setToggleScheduleModal(true);
  };

  // function to close the modal & navigate to menu page
  const scheduleModalCallback = (navigate: boolean = false): void => {
    // close the modal
    setToggleScheduleModal(false);

    // navigate to menu page only if explicitly requested
    if (navigate) {
      router.push("/menu");
    }
  };

  // ==============================|| UI ||============================== //

  return (
    <>
      {orderTypes?.length > 0 && (
        <div className="absolute inset-0 flex flex-col items-start justify-start">
          <div className="pl-[65px] pt-4 pb-4 pr-4 md:pt-8 md:pb-8 md:pr-8 mt-4 banner_form_box">
            <div className="max-w-md home_banner_form home_banner_text">
              <h2>ORDER YOUR MEAL</h2>

              {/* order type component for large screen */}
              <div className="hidden md:block">
                <OrderType />
              </div>

              {/* Form Fields - uses local state */}
              <div className="space-y-4 home_banner_fields">
                {orderType === OrderTypes.PICKUP ? (
                  <PickupAddress
                    selectedLocation={selectedLocation}
                    setSelectedLocation={setSelectedLocation}
                  />
                ) : (
                  <DeliveryAddress
                    deliveryAddressState={deliveryAddressState}
                    setDeliveryAddressState={setDeliveryAddressState}
                    setSelectedLocation={setSelectedLocation}
                  />
                )}

                <div className="flex gap-2 banner_submit_btn">
                  <ScheduleType
                    scheduleType={scheduleType}
                    setScheduleType={setScheduleType}
                    orderType={orderType}
                  />

                  <Button
                    variant="primary"
                    className="w-[120px] font-poppins banner_form_submit_btn"
                    onClick={validateOrderForm}
                  >
                    ENTER
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* schedule modal */}
      {toggleScheduleModal && (
        <Schedule scheduleModalCallback={scheduleModalCallback} />
      )}
    </>
  );
};

export default OrderForm;
