{"name": "ezeats", "version": "0.1.0", "private": true, "scripts": {"dev": "env-cmd -f .env.development next dev --turbopack", "prod": "env-cmd -f .env.production next dev --turbopack", "build": "next build", "build:dev": "env-cmd -f .env.development next build", "build:prod": "env-cmd -f .env.production next build", "start": "next start", "start:dev": "env-cmd -f .env.development next start", "start:prod": "env-cmd -f .env.production next start", "lint": "next lint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@react-google-maps/api": "^2.20.7", "@reduxjs/toolkit": "^2.8.2", "@types/uuid": "^10.0.0", "axios": "^1.11.0", "env-cmd": "^10.1.0", "fast-xml-parser": "^5.2.5", "js-cookie": "^3.0.5", "moment-timezone": "^0.6.0", "next": "15.4.2", "react": "19.1.0", "react-dom": "19.1.0", "react-redux": "^9.2.0", "react-slick": "^0.31.0", "redux-persist": "^6.0.0", "slick-carousel": "^1.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-slick": "^0.23.13", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "typescript": "^5"}}