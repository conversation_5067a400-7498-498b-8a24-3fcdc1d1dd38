import { Metadata } from "next";

import "@/styles/seoPage.styles.css";
import { PageSEODetails } from "@/types";
import { createMetadata } from "@/lib/utils/helperFunctions";

// generate metadata for the SEO static page (SSR)
export const generateMetadata = async (): Promise<Metadata> => {
  // SEO details
  const seoDetails: PageSEODetails = {
    pagetitle: "Best Tiffin Service – Mississauga, Brampton & Toronto | Ezeats",
    h1: "Best Tiffin Service in Mississauga, Brampton & Toronto | EZeats",
    desc: "Looking for an authentic Indian tiffin service in Mississauga, Brampton, or Toronto? Enjoy fast, affordable, and delicious vegetarian and non-vegetarian meals with EZeats. Save time and money with our weekly/ bi-weekly meal subscription plans.",
    keywords:
      "tiffin service, tiffin service near me , indian tiffin service near me, tiffin service mississauga, tiffin service brampton, tiffin service toronto",
  };

  // function call to create Next.js metadata from SEO data
  return createMetadata(seoDetails);
};

// tiffin service in GTA page component
const TiffinServiceInGTA: React.FC = () => {
  return (
    <main>
      {/* hidden h1 for SEO */}
      <h1 className="sr-only">
        Best Tiffin Service in Mississauga, Brampton & Toronto | EZeats
      </h1>

      <div className="page_top_padding"></div>

      <div className="static_breadcrums_parent">
        <div className="static_container">
          <div className="static_breadcrums">
            <a href="/">HOME</a>
            <span>/</span>
            <a>Tiffin Service in GTA</a>
          </div>
        </div>
      </div>

      <div className="custom_sec_11_parent">
        <div className="static_container">
          <div className="custom_sec_11_data custom_sec_11_reverse">
            <div className="custom_sec_11_data_body">
              <div className="custom_sec_11_data_heading">
                <h2>
                  EZeats: Your Go-To Tiffin Service for Authentic Indian Food in
                  the GTA
                </h2>
                <div className="custom_sec_11_des">
                  <p>
                    Ever wish you could enjoy a hot, homemade meal without the
                    effort of cooking or ordering from a restaurant? That’s
                    where <a href="/">EZeats</a> comes in. We’re offering a
                    fresh and convenient way to enjoy delicious, home-cooked
                    Indian food, delivered straight to your door in{" "}
                    <strong>Mississauga, Brampton</strong>, and{" "}
                    <strong>Toronto</strong>. Whether you’re too busy to cook or
                    just want to treat yourself to the authentic taste of India,
                    our tiffin service makes it easy, affordable, and
                    hassle-free.
                  </p>
                </div>
              </div>
              <div className="custom_sec_11_btn">
                <a href="/menu">Order Now</a>
              </div>
            </div>
            <figure>
              <img
                src="https://static.tossdown.com/images/435a1997-7855-4ae0-b832-8fb226c02b6a.webp"
                alt="EZeats: Your Go-To Tiffin Service for Authentic Indian Food in the GTA"
              />
            </figure>
          </div>
        </div>
      </div>

      <section className="cus_sec3_parent">
        <div className="static_container">
          <div className="cus_sec3_box">
            <div className="cus_sec3_data">
              <h2>Why EZeats is Your New Favorite Tiffin Service</h2>
              <div className="cus_sec3_inner_data">
                <p>
                  We know that cooking every day can feel like a challenge, and
                  takeout can get repetitive. That’s why <a href="/">EZeats</a>{" "}
                  offers a solution with our weekly or bi-weekly meal
                  subscriptions. Enjoy fresh, flavorful meals without the need
                  to spend time grocery shopping, cooking, or cleaning up.
                  Whether you’re a vegetarian craving{" "}
                  <a href="/product/palak-paneer-with-roti-306450">
                    palak paneer
                  </a>{" "}
                  or a fan of{" "}
                  <a href="/product/butter-chicken-with-rice-306438">
                    butter chicken
                  </a>
                  , we’ve got something for every taste. Plus, all of our
                  non-vegetarian dishes are halal, catering to a wide range of
                  dietary needs.
                </p>
              </div>
              <div className="cus_sec3_inner_data">
                <h3>A Taste of Home Delivered to You</h3>
                <p>
                  Looking for an <strong>Indian tiffin service</strong> that
                  actually delivers on taste and quality? You’re in the right
                  place. Our menu is packed with the vibrant, comforting flavors
                  of Indian cuisine, from rich curries to deliciously spiced
                  snacks. Whether you're all about the{" "}
                  <a href="/product/bhindi-masala-with-roti-306445">
                    vegetarian dishes
                  </a>{" "}
                  or you want some spice with your meat, we’ve got something
                  that’ll hit the spot every time.
                </p>
              </div>
              <div className="cus_sec3_inner_data">
                <h3>Fast, Reliable Delivery When You Need It Most</h3>
                <p>
                  Waiting for food shouldn’t be part of your stress. We get it.
                  That’s why our <strong> tiffin service</strong> ensures fast
                  and reliable delivery, so you can enjoy your meal when it’s
                  still hot and fresh. Whether you're in{" "}
                  <strong>Mississauga, Brampton, or Toronto, </strong> we’ll get
                  your favorite Indian dishes to you quickly.
                </p>
              </div>
              <div className="cus_sec3_inner_data">
                <h3>Affordable, Convenient Meal Subscriptions</h3>
                <p>
                  Takeout can be expensive. With <a href="/">EZeats</a>, you can
                  enjoy fresh, homemade meals, starting at only $5.99. Our{" "}
                  <strong>tiffin service</strong> is designed to be affordable
                  and convenient. Skip the grocery shopping, avoid the kitchen
                  stress, and still get high-quality meals delivered regularly.
                  With our weekly or bi-weekly plans, you can enjoy the flavors
                  of India at a price that makes sense.
                </p>
              </div>
              <div className="cus_sec3_inner_data">
                <h3>Skip the Cooking and Grocery Shopping</h3>
                <p>
                  What if you could say goodbye to meal planning and grocery
                  lists with <a href="/">EZeats</a>. We take care of everything,
                  from the shopping to the cooking to the delivery. You just
                  sign up, pick your meals, and we’ll do the rest. It’s the
                  ultimate in convenience: fresh, homemade Indian food without
                  any of the work.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="cus_sec4_parent">
        <div className="static_container">
          <div className="cus_sec4_box">
            <div className="cus_sec4_data">
              <h4>Delivering in GTA</h4>
            </div>
          </div>
        </div>
      </section>

      <section className="cus_sec3_parent">
        <div className="static_container">
          <div className="cus_sec3_box">
            <div className="cus_sec3_data">
              <div className="faq_sec">
                <div className="single_faq">
                  <h4>Where can I find a tiffin service near me?</h4>
                  <p>
                    We offer our <strong>tiffin service</strong> in{" "}
                    <strong>Mississauga, Brampton, and Toronto,</strong> so if
                    you’re in these areas, we’ve got you covered.
                  </p>
                </div>
                <div className="single_faq">
                  <h4>Can I choose my meals?</h4>
                  <p>
                    Absolutely! With our{" "}
                    <strong>Indian tiffin service near you,</strong> you can
                    choose from a variety of vegetarian and non-vegetarian
                    dishes based on what you love.
                  </p>
                </div>
                <div className="single_faq">
                  <h4>Are your meals halal?</h4>
                  <p>
                    Yes, all our non-vegetarian meals are halal, prepared with
                    care to meet dietary needs.
                  </p>
                </div>
                <div className="single_faq">
                  <h4>How do I sign up for the meal subscription?</h4>
                  <p>
                    It’s simple! Just visit our website,
                    <a href="/"> ezeats.ca </a> , select your weekly or
                    bi-weekly plan, and we’ll take care of the rest, delivering
                    fresh meals straight to your door.
                  </p>
                  <p>
                    With <a href="/">EZeats</a>, you can skip the cooking, the
                    grocery shopping, and the meal planning. Our{" "}
                    <strong>tiffin service </strong> in{" "}
                    <strong>Mississauga, Brampton, </strong> and{" "}
                    <strong>Toronto</strong> gives you the convenience of
                    home-cooked meals, delivered fresh and hot to your doorstep.
                  </p>
                  <p>
                    Ready to make mealtime simpler? Sign up for our{" "}
                    <strong>tiffin service</strong> today and enjoy more time
                    for the things you love!
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
};

export default TiffinServiceInGTA;
