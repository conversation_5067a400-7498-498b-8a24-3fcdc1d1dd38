import React from "react";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSpinner } from "@fortawesome/free-solid-svg-icons";

import { ButtonProps } from "../index.types";

const Button: React.FC<ButtonProps> = ({
  children,
  variant = "primary",
  size = "medium",
  disabled = false,
  loading = false,
  className = "",
  onClick,
  type = "button",
  ...props
}) => {
  // ==============================|| styles ||============================== //

  const baseClasses =
    "font-medium flex items-center justify-center focus:outline-none cursor-pointer disabled:cursor-not-allowed whitespace-nowrap";

  const variantClasses = {
    primary: "bg-black text-white disabled:bg-gray-300 disabled:text-gray-500",
    secondary:
      "bg-white text-black border border-gray-300 disabled:bg-gray-100 disabled:text-gray-400",
    outline:
      "bg-transparent text-black border border-black disabled:border-gray-300 disabled:text-gray-400",
    ghost: "bg-transparent text-black disabled:text-gray-400",
    danger: "bg-red-500 text-white disabled:bg-red-300",
    success: "bg-green-500 text-white disabled:bg-green-300",
  };

  const sizeClasses = {
    small: "px-3 py-1.5 text-sm",
    medium: "px-6 py-2 text-sm",
    large: "px-8 py-3 text-base",
  };

  const combinedClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;

  // ==============================|| UI ||============================== //

  return (
    <button
      type={type}
      className={combinedClasses}
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {loading ? (
        <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
      ) : (
        children
      )}
    </button>
  );
};

export default Button;
