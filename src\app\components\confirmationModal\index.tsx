"use client";

import React from "react";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faXmark } from "@fortawesome/free-solid-svg-icons";

import { Button } from "@/app/components";
import { ConfirmationModalProps } from "../index.types";

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  open,
  title,
  message,
  confirmText = "Yes, Continue",
  cancelText = "Cancel",
  onConfirm,
  onCancel,
}) => {
  if (!open) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/50 z-10 flex items-center justify-center">
      <div className="bg-white w-full max-w-md p-5">
        {/* header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-gray-900">{title}</h2>

          <Button
            title="Close modal"
            variant="ghost"
            className="!p-1 text-gray-400 hover:text-gray-600"
            onClick={onCancel}
          >
            <FontAwesomeIcon icon={faXmark} className="!w-5 !h-5" />
          </Button>
        </div>

        {/* message */}
        <div className="mb-6">
          <p className="text-gray-700 leading-relaxed">{message}</p>
        </div>

        {/* action buttons */}
        <div className="flex gap-3 justify-end">
          <Button
            title="Cancel action"
            variant="ghost"
            className="!bg-gray-100 !text-gray-700 hover:!bg-gray-200"
            onClick={onCancel}
          >
            {cancelText}
          </Button>

          <Button title="Confirm action" variant="primary" onClick={onConfirm}>
            {confirmText}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal;
