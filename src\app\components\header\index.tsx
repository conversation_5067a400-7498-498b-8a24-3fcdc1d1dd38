import Link from "next/link";
import Image from "next/image";

import { NavigationMenu } from "@/app/components";
import ClientView from "./clientView";
import { businessName, headerLogo } from "@/lib/utils/constants";

const Header: React.FC = () => {
  return (
    <>
      {/* top bar */}
      <div className="bg-black !min-h-[42px] w-full"></div>

      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="static_container mx-auto max-w-6xl flex items-center justify-between px-2.5 lg:px-0 py-3 md:py-[21px]">
          <div className="flex items-center">
            <Link href="/" className="flex-shrink-0">
              <Image
                src={headerLogo}
                alt={businessName}
                width={170}
                height={59}
                priority
                className="object-contain w-[84px] h-[29px] md:w-[170px] md:h-[59px]"
              />
            </Link>

            {/* desktop navigation */}
            <nav>
              <div className="hidden md:flex items-center gap-7 ml-8">
                <NavigationMenu className="text-sm font-medium hover:text-yellow-500" />
              </div>
            </nav>
          </div>

          {/* client side UI */}
          <ClientView />
        </div>
      </header>
    </>
  );
};

export default Header;
