import { NextResponse } from "next/server";

import axios from "@/lib/axios";

import { BusinessDetails } from "@/types";
import { getBusinessDetails, handleBusinessesResponse } from "@/lib/apiConfigs";

// get business details API route handler
export async function GET() {
  try {
    // get business details API call
    const { data } = await axios(getBusinessDetails());

    // verify API response
    if (
      data?.status === 200 &&
      data?.result &&
      data?.result?.branches?.length
    ) {
      // function to handle the business details API response
      const businessDetails: BusinessDetails = handleBusinessesResponse(
        data?.result
      );

      // return success response
      return NextResponse.json(
        { data: businessDetails, meta: null, error: null },
        { status: 200 }
      );
    }

    // return error if business data not found
    return NextResponse.json(
      { data: null, meta: null, error: null },
      { status: 404 }
    );
  } catch (error) {
    // return error response
    return NextResponse.json(
      { data: null, meta: null, error },
      { status: 500 }
    );
  }
}
