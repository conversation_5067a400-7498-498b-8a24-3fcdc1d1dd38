@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-poppins);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-poppins), Arial, Helvetica, sans-serif;
}

/* container class */
.static_container {
  max-width: 1210px;
  margin: 0 auto;
}

@layer utilities {
  .icon-size {
    @apply w-4 h-4;
  }

  /* heading font */
  .font-heading {
    font-family: var(--font-anton), Anton, sans-serif !important;
  }
}
