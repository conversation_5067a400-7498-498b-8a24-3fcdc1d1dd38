import type { Metadata } from "next";
import { stripHtml, formatSeoDescription } from "@/lib/seo-utils";

import { getPageSeo, getProductSeo } from "@/services/seo-service";

// Default metadata as fallback (only for title)
const DEFAULT_TITLE = "EZeats - Fresh Meal Delivery";

/**
 * Generates metadata for a specific page using its XML page name
 */
export async function generatePageMetadata(
  pageName: string
): Promise<Metadata> {
  try {
    const pageSeo = await getPageSeo(pageName);

    if (!pageSeo) {
      console.warn(`Using default title for page: ${pageName}`);
      return {
        title: DEFAULT_TITLE,
      };
    }

    // Create metadata object with only the values from XML
    const metadata: Metadata = {
      title: pageSeo.seo.pagetitle || DEFAULT_TITLE,
    };

    // Only add description if it exists in the XML
    if (pageSeo.seo.desc) {
      metadata.description = pageSeo.seo.desc;
    }

    // Only add keywords if they exist in the XML
    if (pageSeo.seo.keywords) {
      metadata.keywords = pageSeo.seo.keywords;
    }

    return metadata;
  } catch (error) {
    console.error(`Error generating metadata for page ${pageName}:`, error);
    return {
      title: DEFAULT_TITLE,
    };
  }
}

/**
 * Generates metadata for a product detail page from product JSON, XML and final fallbacks
 */
export async function generateProductMetadataFromSources(p: {
  name: string;
  description?: string;
  seo?: {
    title?: string;
    h1?: string;
    description?: string;
    keywords?: string;
  } | null;
}): Promise<Metadata> {
  const xml = await getProductSeo(p.name, stripHtml(p.description) || "");

  const pick = (v?: string | null) =>
    typeof v === "string" && v.trim() ? v.trim() : "";

  const title = pick(p.seo?.title) || pick(xml?.pagetitle) || p.name;
  const description =
    pick(stripHtml(p.seo?.description)) ||
    pick(stripHtml(xml?.desc)) ||
    stripHtml(p.description) ||
    "";
  const keywords = pick(p.seo?.keywords) || pick(xml?.keywords) || p.name;

  const metadata: Metadata = { title };
  if (description)
    metadata.description = formatSeoDescription(description, 150);
  if (keywords) metadata.keywords = keywords;
  return metadata;
}
