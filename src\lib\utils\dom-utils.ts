/**
 * Utility functions for safe DOM manipulation
 */

/**
 * Patches the removeChild method to handle errors gracefully
 * This is a defensive measure against external scripts that might cause removeChild errors
 */
export function patchRemoveChild(): () => void {
  const originalRemoveChild = Node.prototype.removeChild;

  Node.prototype.removeChild = function <T extends Node>(child: T): T {
    try {
      // Check if the child is actually a child of this node
      if (!this.contains(child)) {
        console.warn(
          "Attempted to remove a node that is not a child of this parent",
          {
            parent: this,
            child: child,
          }
        );
        return child as T;
      }

      return originalRemoveChild.call(this, child);
    } catch (error) {
      console.warn("removeChild error caught and handled:", error, {
        parent: this,
        child: child,
      });
      return child as T;
    }
  };

  // Return a function to restore the original method
  return () => {
    Node.prototype.removeChild = originalRemoveChild;
  };
}

/**
 * Safely removes a DOM element from its parent
 * @param element - The element to remove
 * @param expectedParent - The expected parent element (optional)
 * @param errorMessage - Custom error message for logging (optional)
 */
export function safeRemoveElement(
  element: Element | null,
  expectedParent?: Element | null,
  errorMessage?: string
): void {
  if (!element) return;

  try {
    // If expectedParent is provided, verify the element is actually a child
    if (expectedParent && element.parentNode !== expectedParent) {
      console.warn(
        errorMessage || "Element is not a child of expected parent",
        { element, expectedParent, actualParent: element.parentNode }
      );
      return;
    }

    // If no expectedParent provided, just check if element has a parent
    if (!expectedParent && !element.parentNode) {
      console.warn(errorMessage || "Element has no parent node", { element });
      return;
    }

    // Remove the element
    const parent = expectedParent || element.parentNode;
    if (parent) {
      parent.removeChild(element);
    }
  } catch (error) {
    console.warn(errorMessage || "Failed to remove DOM element", {
      error,
      element,
      expectedParent,
    });
  }
}

/**
 * Safely removes multiple DOM elements from their parents
 * @param elements - Array of elements to remove
 * @param expectedParent - The expected parent element (optional)
 * @param errorMessage - Custom error message for logging (optional)
 */
export function safeRemoveElements(
  elements: (Element | null)[],
  expectedParent?: Element | null,
  errorMessage?: string
): void {
  elements.forEach((element, index) => {
    safeRemoveElement(
      element,
      expectedParent,
      errorMessage ? `${errorMessage} (element ${index})` : undefined
    );
  });
}

/**
 * Safely removes a script element from document.body
 * @param scriptId - The ID of the script element to remove
 * @param errorMessage - Custom error message for logging (optional)
 */
export function safeRemoveScript(
  scriptId: string,
  errorMessage?: string
): void {
  const script = document.getElementById(scriptId);
  safeRemoveElement(
    script,
    document.body,
    errorMessage || `Failed to remove script: ${scriptId}`
  );
}

/**
 * Safely removes a link element from document.head
 * @param linkId - The ID of the link element to remove
 * @param errorMessage - Custom error message for logging (optional)
 */
export function safeRemoveLink(linkId: string, errorMessage?: string): void {
  const link = document.getElementById(linkId);
  safeRemoveElement(
    link,
    document.head,
    errorMessage || `Failed to remove link: ${linkId}`
  );
}

/**
 * Safely removes a link element by href from document.head
 * @param href - The href of the link element to remove
 * @param errorMessage - Custom error message for logging (optional)
 */
export function safeRemoveLinkByHref(
  href: string,
  errorMessage?: string
): void {
  const link = document.head.querySelector(`link[href="${href}"]`);
  safeRemoveElement(
    link,
    document.head,
    errorMessage || `Failed to remove link with href: ${href}`
  );
}

/**
 * Safely removes elements matching a selector
 * @param selector - CSS selector for elements to remove
 * @param errorMessage - Custom error message for logging (optional)
 */
export function safeRemoveBySelector(
  selector: string,
  errorMessage?: string
): void {
  const elements = document.querySelectorAll(selector);
  elements.forEach((element) => {
    safeRemoveElement(
      element,
      element.parentNode as Element,
      errorMessage || `Failed to remove element matching selector: ${selector}`
    );
  });
}
