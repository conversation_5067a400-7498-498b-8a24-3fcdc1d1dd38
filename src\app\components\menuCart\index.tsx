"use client";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlus, faMinus } from "@fortawesome/free-solid-svg-icons";

import { useCart } from "@/lib/hooks/useCart";

import { Button } from "@/app/components";
import { ProductCardProps } from "@/app/components/index.types";
import { CartActions } from "@/types";
import { useSelector } from "@/lib/store";

const MenuCart: React.FC<ProductCardProps> = ({ product }) => {
  const { cartDetails } = useSelector((state) => state.order);

  // ==============================|| custom hooks ||============================== //

  // custom hook to manage cart
  const { updateCart, cartState } = useCart();

  // ==============================|| handler functions ||============================== //

  // function to handle cart change
  const handleCartChange = async (cartAction: CartActions) => {
    // function to call cart API
    await updateCart(cartAction, product);
  };

  // ==============================|| UI ||============================== //

  return (
    <>
      {product?.inStock ? (
        <div className="w-full border border-gray-300 rounded-full flex items-center justify-between px-4 py-1 mb-3">
          <Button
            title="Remove from cart"
            variant="ghost"
            size="small"
            loading={
              cartState?.loading === `${CartActions.UPDATE}-${product?.id}`
            }
            onClick={() => handleCartChange(CartActions.UPDATE)}
          >
            <FontAwesomeIcon icon={faMinus} />
          </Button>

          <div className="text-md font-medium">
            {cartDetails?.items?.[product?.id]?.quantity || 0}
          </div>

          <Button
            title="Add to cart"
            variant="ghost"
            size="small"
            loading={cartState?.loading === `${CartActions.ADD}-${product?.id}`}
            onClick={() => handleCartChange(CartActions.ADD)}
          >
            <FontAwesomeIcon icon={faPlus} />
          </Button>
        </div>
      ) : (
        <div className="flex items-center text-red-600 font-medium text-sm mb-3 h-[35px]">
          OUT OF STOCK
        </div>
      )}
    </>
  );
};

export default MenuCart;
