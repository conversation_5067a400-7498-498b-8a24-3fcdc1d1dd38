import { Metadata } from "next";

import ClientView from "./clientView";
import { Testimonials } from "@/app/components";
import "@/styles/kitchen.styles.css";
import { PageSEODetails } from "@/types";
import { createMetadata } from "@/lib/utils/helperFunctions";
import { getPageSEOData } from "@/lib/apiConfigs";

// generate metadata for the SEO static page (SSR)
export const generateMetadata = async (): Promise<Metadata> => {
  // function to call SEO API to fetch page SEO data
  const seoDetails: PageSEODetails = await getPageSEOData("about");

  // function call to create Next.js metadata from SEO data
  return createMetadata(seoDetails);
};

// our kitchen page component
const OurKitchen: React.FC = async () => {
  // ==============================|| API calls ||============================== //

  // function to call SEO API to fetch page h1
  const seoDetails: PageSEODetails = await getPageSEOData("about");

  // ==============================|| UI ||============================== //

  return (
    <main>
      {/* hidden h1 for SEO */}
      <h1 className="sr-only">{seoDetails?.h1}</h1>

      <section className="static_banner_section">
        <div className="static_banner_arrows">
          <div className="static_bannerSlider_arrow_box"></div>
        </div>
        <div className="static_Banner_slider">
          <div className="static_Banner">
            <figure>
              <img
                src="https://static.tossdown.com/images/cdd8b8f9-c55d-44c5-b18e-0ebc6e484258.webp"
                alt="Banner desktop"
                className="banner_desk_img"
              />
              <img
                src="https://static.tossdown.com/images/cdd8b8f9-c55d-44c5-b18e-0ebc6e484258.webp"
                alt="Banner mobile"
                className="banner_mobile_img"
              />
            </figure>
          </div>
        </div>
      </section>

      <div className="working_process_parent">
        <div className="working_process_parent">
          <div className="working_process_heading">
            <div className="process_heading_parent">
              <figure>
                <img
                  className="custom_image_sec"
                  src="https://static.tossdown.com/site/c8c38c9b-4193-47f4-ae88-4615fb5f01a9.webp"
                  alt="Kitchen icon"
                />
              </figure>
              <div className="process_heading_custom">
                <h2>welcome to OUR KITCHEN</h2>

                <small>where delicious memories are made!</small>
              </div>
            </div>
          </div>

          <div className="working_process_main">
            <ClientView />

            <div className="custom_13_parent">
              <div className="custom_13">
                <div className="custom_13_content">
                  <h2>YOUR BENEFITS</h2>
                  <ul>
                    <li>
                      Whether you're a busy professional, a student, or a family
                      on-the-go, Ezeats has got you covered with our easy online
                      ordering and fast delivery service.
                    </li>
                    <li>
                      Whether you're in the mood for spicy Indian dishes, rich
                      Italian pasta, or some Asian flavors, EZeats has got them
                      all.
                    </li>
                    <li>
                      Enjoy our customizable and flexible subscription plans or
                      use our One-time ordering system that fits your schedule
                      and budget.
                    </li>
                    <li>
                      With prices starting at just $5.99 per serving, you can
                      enjoy restaurant-quality and budget-friendly meals,
                      without the hassle of cooking or cleaning up.
                    </li>
                  </ul>
                </div>
              </div>

              <figure className="custom_13_dek_img">
                <img
                  src="https://static.tossdown.com/images/777332ca-658b-4c70-806e-b8e2c54ea9b7.webp"
                  alt="Benefits desktop"
                />
              </figure>

              <figure className="custom_13_mob_img">
                <img
                  src="https://static.tossdown.com/images/b2e9cd4b-b8ad-41b3-baae-f65bce2a9601.webp"
                  alt="Benefits mobile"
                />
              </figure>
            </div>

            <div className="custom_12">
              <img
                className="custom_12_ab_1"
                src="https://static.tossdown.com/images/0b2c1b68-0fc1-4bd3-a98d-7ccbd29ff89b.webp"
                alt="Goal decoration"
              />

              <div className="custom_12_body">
                <div className="custom_12_heading">
                  <h3>Our GOAL</h3>
                  <p>
                    Our goal is to provide affordable, convenient, and delicious
                    meals that fit your busy lifestyle. We believe that everyone
                    deserves to enjoy a good meal, regardless of their budget or
                    cooking skills. We strive to make mealtime a stress-free and
                    enjoyable experience without compromising on flavor or
                    breaking the bank.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <section className="custom_sec_14">
        <div className="custom_container">
          <div className="custom_14_data_parent">
            <div className="custom_14_data_1">
              <strong>Say Goodbye</strong> <span>to meal prep stress AND</span>
            </div>

            <div className="custom_14_data_2">
              <strong>HELLO</strong>{" "}
              <span>
                to more free time with ez<small>eats!</small>
              </span>
            </div>

            <p>THE PERFECT SOLUTION FOR A QUICK, EASY, AND AFFORDABLE MEAL!</p>
          </div>

          <div className="custom_14_data_parent_2">
            <strong>
              join <small>us!</small>
            </strong>{" "}
            <span></span>
            <p>
              Sign up today and discover the convenience of EZeats, your
              one-stop-shop for all your food cravings! With a diverse range of
              meals to choose from, you can indulge in your favorite dishes from
              the comfort of your home. With just a few clicks, you can order
              mouth-watering meals that cater to your needs and preferences in
              just a few hours.
            </p>
          </div>
        </div>
      </section>

      {/* testimonials/reviews */}
      <Testimonials />
    </main>
  );
};

export default OurKitchen;
