import { Metadata } from "next";

import ClientView from "../clientView";
import { PageSEODetails } from "@/types";
import { createMetadata } from "@/lib/utils/helperFunctions";

// generate metadata for the profile sub-pages (SSR)
export const generateMetadata = async ({ params }: { params: { slug: string[] } }): Promise<Metadata> => {
  // get the current route from params
  const currentRoute = params.slug?.[0] || "profile";
  
  // create dynamic SEO details based on the route
  const getProfileSEODetails = (route: string): PageSEODetails => {
    switch (route) {
      case "subscriptions":
        return {
          pagetitle: "Subscriptions - EZeats Fresh Meal Delivery Service",
          h1: "Manage Your Subscriptions - Fresh Meals Delivered",
          desc: "View and manage your meal subscriptions. Weekly and bi-weekly fresh meal delivery plans in GTA.",
          keywords: "subscriptions, meal plans, weekly delivery, bi-weekly delivery, EZeats, GTA",
        };
      case "orders":
        return {
          pagetitle: "Order History - EZeats Fresh Meal Delivery Service",
          h1: "Your Order History - Fresh Meals Delivered",
          desc: "View your complete order history and track current orders. Fresh meal delivery service in GTA.",
          keywords: "order history, track orders, meal delivery, food delivery, EZeats, GTA",
        };
      case "activity-log":
        return {
          pagetitle: "Activity Log - EZeats Fresh Meal Delivery Service",
          h1: "Your Activity Log - Fresh Meals Delivered",
          desc: "View your account activity and transaction history. Fresh meal delivery service in GTA.",
          keywords: "activity log, account activity, transaction history, EZeats, GTA",
        };
      default:
        return {
          pagetitle: "Profile - EZeats Fresh Meal Delivery Service",
          h1: "Manage Your Profile - Fresh Meals Delivered",
          desc: "Manage your profile, view orders, subscriptions, and account settings. Your personal dashboard for fresh meal delivery service in GTA.",
          keywords: "profile, account, orders, subscriptions, meal delivery, food delivery, EZeats, GTA",
        };
    }
  };

  // function call to create Next.js metadata from SEO data
  return createMetadata(getProfileSEODetails(currentRoute));
};

// profile sub-pages component
const ProfileSubPage: React.FC<{ params: { slug: string[] } }> = ({ params }) => {
  const currentRoute = params.slug?.[0] || "profile";
  
  // create dynamic h1 text based on route
  const getH1Text = (route: string): string => {
    switch (route) {
      case "subscriptions":
        return "Manage Your Subscriptions - Fresh Meals Delivered";
      case "orders":
        return "Your Order History - Fresh Meals Delivered";
      case "activity-log":
        return "Your Activity Log - Fresh Meals Delivered";
      default:
        return "Manage Your Profile - Fresh Meals Delivered";
    }
  };

  return (
    <main>
      {/* hidden h1 for SEO */}
      <h1 className="sr-only">{getH1Text(currentRoute)}</h1>

      {/* client side UI */}
      <ClientView />
    </main>
  );
};

export default ProfileSubPage;
