import { useState } from "react";

import {
  RequestState,
  ProductsList,
  Cart,
  CartActions,
  CartItem,
} from "@/types";
import { mealBoxSize } from "@/lib/utils/constants";
import { createCartId, userNotification } from "@/lib/utils/helperFunctions";
import { callCartAbstractedAPI } from "@/lib/apiConfigs";
import { useSelector, dispatch } from "@/lib/store";
import { updateOrderStore } from "@/lib/store/slices/order";

/**
 * module-level flag to prevent multiple cart operations
 * using variable to prevent async update issues
 * using variable outside of hook to prevent multiple instances
 */
let preventCartAPI: boolean = false;

export const useCart = () => {
  const [cartState, setCartState] = useState<RequestState>({
    loading: "",
    error: "",
  });

  const { orderType, location, cartId, boxSize, cartDetails } = useSelector(
    (state) => state.order
  );

  // ==============================|| handler functions ||============================== //

  // function to validate cart constraints based on action
  const validateCart = (
    action: CartActions,
    product: ProductsList | CartItem
  ): boolean => {
    /**
     * if => validation for ADD action
     * else-if => validation for UPDATE action
     */
    if (action === CartActions.ADD) {
      /**
       * if => verify the box size limit
       * else-if => verify inventory availability
       */
      if (cartDetails?.itemsCount >= boxSize) {
        // function to set message to notify user
        userNotification(
          boxSize === mealBoxSize.SMALL
            ? `To add more meals, please update your cart to ${mealBoxSize.MEDIUM} meals`
            : "You have reached your maximum meal limit for your box size, please proceed to checkout"
        );

        return false;
      } else if (
        !parseInt(product.inventory) ||
        cartDetails?.items?.[product.id]?.quantity ===
          parseInt(product.inventory)
      ) {
        // function to set message to notify user
        userNotification(
          `Oops! We only have ${product.inventory} of this item in stock right now.`
        );

        return false;
      }
    } else if (action === CartActions.UPDATE) {
      // verify if item exists in cart
      if ((cartDetails?.items?.[product.id]?.quantity || 0) <= 0) {
        return false;
      }
    }

    return true;
  };

  // function to update cart
  const updateCart = async (
    action: CartActions,
    product: ProductsList | CartItem
  ): Promise<void> => {
    // block further calls immediately
    if (preventCartAPI) {
      return;
    }

    // set to true to prevent further API call
    preventCartAPI = true;

    try {
      // verify cart constraints based on action
      if (!validateCart(action, product)) {
        // reset on invalid action
        preventCartAPI = false;

        return;
      }

      // update loading state with item ID and action
      setCartState({ loading: `${action}-${product.id}`, error: "" });

      // generate cart ID if not exists
      const orderCartId: string = cartId || createCartId();

      // cart abstracted API call
      const { data } = await callCartAbstractedAPI({
        businessId: process.env.NEXT_PUBLIC_BUSINESS_ID || "",
        branchId: location?.id || "",
        action,
        orderCartId,
        orderType,
        product,
        cartDetails,
      });

      // verify if data exists
      if ((data as Cart)?.items) {
        // update cart details in store
        dispatch(updateOrderStore({ type: "cartDetails", value: data }));

        // show success message only for add to cart
        if (action === CartActions?.ADD) {
          // function to set message to notify user
          userNotification("Item added to cart successfully", "success");
        }
      } else {
        // function to set message to notify user
        userNotification("Failed to update cart. Please try again");
      }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      // function to set message to notify user
      userNotification("Failed to update cart. Please try again");
    } finally {
      // reset cart operation state
      preventCartAPI = false;

      // update loading state
      setCartState({ loading: "", error: "" });
    }
  };

  return { updateCart, cartState };
};
