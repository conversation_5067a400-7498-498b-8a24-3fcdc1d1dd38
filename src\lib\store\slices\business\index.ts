import { createSlice } from "@reduxjs/toolkit";

import { BusinessDetails } from "@/types";

// initial state
const initialState: BusinessDetails = {
  name: "",
  currency: "",
  minSpend: 0,
  inventory: 0,
  contact: "",
  email: "",
  decimalPlaces: 0,
  addressKey: "",
  locations: [],
};

const Business = createSlice({
  name: "business",
  initialState,
  reducers: {
    updateBusinessStore(state, action) {
      state[action.payload.type] = action.payload.value;
    },
    bulkUpdateBusinessStore(state, action) {
      return { ...state, ...action.payload };
    },
  },
});

export default Business.reducer;

export const { updateBusinessStore, bulkUpdateBusinessStore } =
  Business.actions;
