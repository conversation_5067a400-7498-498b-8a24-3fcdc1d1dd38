import React from "react";
import Image from "next/image";
import Link from "next/link";

import { MenuCart, Rating } from "@/app/components";
import { ProductCardProps } from "../index.types";
import { decimalPlaces, productPlaceholder } from "@/lib/utils/constants";
import {
  getFormattedPrice,
  generateProductSlug,
} from "@/lib/utils/helperFunctions";

const ProductCard: React.FC<ProductCardProps> = ({ product, enableCart }) => {
  // generate product slug for navigation
  const productSlug = generateProductSlug(product?.name, product?.id);

  return (
    <div className="flex flex-col items-center w-full">
      <Link
        href={`/product/${productSlug}`}
        className="relative w-full aspect-square mb-3 overflow-hidden cursor-pointer hover:opacity-90 transition-opacity"
      >
        {/* image */}
        <Image
          src={product?.image || productPlaceholder}
          alt={product?.name}
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 280px"
          className="object-cover"
        />

        {/* attribute */}
        {product?.attributes && (
          <div className="absolute top-2 left-2 bg-black text-white text-xs px-2 py-1 rounded-sm">
            {product?.attributes}
          </div>
        )}
      </Link>

      {/* cart buttons => display only on menu page */}
      {enableCart && <MenuCart product={product} />}

      {/* name */}
      <Link href={`/product/${productSlug}`}>
        <h3 className="text-center font-poppins font-semibold text-black text-base md:text-lg mb-[6px] leading-[22px] w-full max-w-full h-[22px] max-h-[22px] overflow-hidden text-ellipsis line-clamp-1 cursor-pointer">
          {product?.name}
        </h3>
      </Link>

      {/* rating */}
      <div className="flex justify-center mt-2 mb-4 mx-0">
        <Rating rating={parseFloat(product?.rating) || 0} />
      </div>

      {/* price */}
      <p className="text-center font-medium">
        {getFormattedPrice(
          parseFloat(product?.price) || 0,
          product?.currency,
          decimalPlaces
        )}
      </p>
    </div>
  );
};

export default ProductCard;
